"""
Z-Waif 호환 API 컨트롤러
Z-Waif의 api_controller.py와 동일한 구조
"""

import datetime
import os
import html
import json
import base64
import time
import random
from typing import List, Dict, Optional, Any

import requests
import sseclient
from dotenv import load_dotenv

import utils.cane_lib
import utils.based_rag
import utils.zw_logging
import utils.settings
import utils.retrospect
import utils.lorebook
import utils.tag_task_controller
import utils.voice_splitter
import utils.voice
import utils.vtube_studio
import utils.hangout

import API.oobaooga_api
import API.ollama_api
import API.character_card

load_dotenv()

# 전역 변수 (Z-Waif와 동일)
received_message = ""
ooga_history = []
forced_token_level = 0
force_token_count = False
currently_sending_message = ""
currently_streaming_message = ""
last_message_streamed = False
is_in_api_request = False
last_message_received_has_own_name = False

# API 설정
API_TYPE = os.getenv("API_TYPE", "Oobabooga")
API_TYPE_VISUAL = os.getenv("API_TYPE_VISUAL", "Oobabooga")
HOST_PORT = os.getenv("HOST_PORT", "127.0.0.1:5000")
IMG_PORT = os.getenv("IMG_PORT", "127.0.0.1:5000")
CHARACTER_CARD = os.getenv("CHARACTER_CARD", "CharacterCard.yaml")

# URI 구성
URI = f"http://{HOST_PORT}/v1/chat/completions"
IMG_URI = f"http://{IMG_PORT}/v1/chat/completions"

def send_via_oogabooga(user_input: str, temp_level: float = 0.7):
    """
    Z-Waif의 send_via_oogabooga와 동일한 함수
    Oobabooga API를 통해 메시지 전송
    """
    global received_message, ooga_history, is_in_api_request
    global currently_sending_message, currently_streaming_message
    global last_message_streamed
    
    # API 요청 시작
    is_in_api_request = True
    currently_sending_message = user_input
    currently_streaming_message = ""
    last_message_streamed = False
    
    # 사용자 이름 체크
    check_for_name_in_message(user_input)
    
    try:
        # 히스토리 로드
        with open("LiveLog.json", 'r', encoding='utf-8') as f:
            ooga_history = json.load(f)
    except FileNotFoundError:
        ooga_history = []
        print("LiveLog.json not found, starting with empty history")
    
    # 메시지 인코딩
    messages_to_send = encode_new_api(user_input)
    
    # 토큰 수 계산
    max_context = int(os.getenv("TOKEN_LIMIT", "4096"))
    cur_tokens_required = utils.settings.max_tokens
    
    # 캐릭터 설정
    char_send = utils.settings.char_name
    if not char_send:
        char_send = CHARACTER_CARD
    
    # 정지 문자열
    stop = utils.settings.stopping_strings
    
    # 프리셋 설정
    preset = 'Z-Waif-ADEF-Standard'
    if utils.settings.model_preset != "Default":
        preset = utils.settings.model_preset
    
    # API 요청 전송
    if API_TYPE == "Oobabooga":
        request = {
            "messages": messages_to_send,
            'max_tokens': cur_tokens_required,
            'mode': 'chat',
            'character': char_send,
            'truncation_length': max_context,
            'stop': stop,
            'preset': preset,
            'temperature': temp_level
        }
        
        received_message = API.oobaooga_api.api_standard(request)
        
    elif API_TYPE == "Ollama":
        received_message = API.ollama_api.api_standard(
            history=messages_to_send,
            temp_level=temp_level,
            stop=stop,
            max_tokens=cur_tokens_required
        )
    
    # 메시지 후처리
    received_message = html.unescape(received_message)
    
    # RP 억제
    if utils.settings.supress_rp:
        received_message = supress_rp_as_others(received_message)
    
    # 히스토리에 추가
    add_to_history(user_input, received_message)
    
    # API 요청 완료
    is_in_api_request = False
    
    return received_message

def receive_via_oogabooga() -> str:
    """
    Z-Waif의 receive_via_oogabooga와 동일한 함수
    API 응답 수신
    """
    global received_message
    return received_message

def next_message_oogabooga():
    """다음 메시지 생성 (Z-Waif와 동일)"""
    global received_message, is_in_api_request
    
    is_in_api_request = True
    
    try:
        # 히스토리 로드
        with open("LiveLog.json", 'r', encoding='utf-8') as f:
            ooga_history = json.load(f)
        
        # 마지막 사용자 메시지 가져오기
        last_user_message = ""
        for msg in reversed(ooga_history):
            if msg.get("role") == "user":
                last_user_message = msg.get("content", "")
                break
        
        if last_user_message:
            # 새로운 응답 생성
            received_message = generate_new_response(last_user_message)
            
            # 히스토리 업데이트 (마지막 AI 응답 교체)
            for i in reversed(range(len(ooga_history))):
                if ooga_history[i].get("role") == "assistant":
                    ooga_history[i]["content"] = received_message
                    break
            
            # 히스토리 저장
            save_history()
    
    except Exception as e:
        print(f"Next message error: {e}")
        received_message = "죄송해요, 새로운 응답을 생성할 수 없었어요."
    
    finally:
        is_in_api_request = False

def encode_new_api(user_input: str) -> List[Dict]:
    """메시지 인코딩 (Z-Waif와 동일한 구조)"""
    messages = []
    
    # 시스템 메시지 추가
    system_message = API.character_card.get_character_card()
    if system_message:
        messages.append({"role": "system", "content": system_message})
    
    # 시간 정보 추가
    if utils.settings.time_in_encoding:
        current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        messages.append({"role": "system", "content": f"Current time: {current_time}"})
    
    # RAG 메모리 추가
    if utils.settings.rag_enabled:
        relevant_memories = utils.based_rag.search_relevant_memories(user_input)
        if relevant_memories:
            memory_context = "Relevant memories:\n" + "\n".join(relevant_memories[:3])
            messages.append({"role": "system", "content": memory_context})
    
    # 히스토리 추가 (제한된 수만)
    recent_history = ooga_history[-utils.settings.message_pair_limit*2:] if ooga_history else []
    messages.extend(recent_history)
    
    # 현재 사용자 메시지 추가
    messages.append({"role": "user", "content": user_input})
    
    return messages

def check_for_name_in_message(message: str):
    """메시지에 AI 이름이 포함되어 있는지 확인"""
    global last_message_received_has_own_name
    
    char_name = utils.settings.char_name.lower()
    message_lower = message.lower()
    
    last_message_received_has_own_name = char_name in message_lower

def supress_rp_as_others(message: str) -> str:
    """다른 캐릭터로의 RP 억제"""
    # 간단한 RP 억제 로직
    lines = message.split('\n')
    filtered_lines = []
    
    for line in lines:
        # *action* 형태의 액션 제거
        if line.strip().startswith('*') and line.strip().endswith('*'):
            continue
        # "Character:" 형태의 다른 캐릭터 대화 제거
        if ':' in line and not line.startswith(utils.settings.char_name):
            continue
        filtered_lines.append(line)
    
    return '\n'.join(filtered_lines)

def add_to_history(user_input: str, ai_response: str):
    """히스토리에 대화 추가"""
    global ooga_history
    
    # 사용자 메시지 추가
    ooga_history.append({
        "role": "user",
        "content": user_input,
        "timestamp": datetime.datetime.now().isoformat()
    })
    
    # AI 응답 추가
    ooga_history.append({
        "role": "assistant",
        "content": ai_response,
        "timestamp": datetime.datetime.now().isoformat()
    })
    
    # 히스토리 길이 제한
    max_history = utils.settings.message_pair_limit * 2
    if len(ooga_history) > max_history:
        ooga_history = ooga_history[-max_history:]
    
    # 파일에 저장
    save_history()
    
    # RAG에 저장
    if utils.settings.rag_enabled:
        utils.based_rag.add_to_memory(user_input, ai_response)

def save_history():
    """히스토리를 파일에 저장"""
    try:
        with open("LiveLog.json", 'w', encoding='utf-8') as f:
            json.dump(ooga_history, f, ensure_ascii=False, indent=2)
    except Exception as e:
        print(f"History save error: {e}")

def check_load_past_chat():
    """과거 채팅 로드"""
    global ooga_history
    
    try:
        with open("LiveLog.json", 'r', encoding='utf-8') as f:
            ooga_history = json.load(f)
        print(f"✅ Loaded {len(ooga_history)} messages from history")
    except FileNotFoundError:
        ooga_history = []
        print("No previous chat history found")
    except Exception as e:
        print(f"Error loading chat history: {e}")
        ooga_history = []

def generate_new_response(user_input: str) -> str:
    """새로운 응답 생성 (내부 함수)"""
    # 실제 API 호출 로직은 send_via_oogabooga와 유사
    # 여기서는 간단한 구현
    return f"새로운 응답: {user_input}에 대한 다른 답변입니다."
