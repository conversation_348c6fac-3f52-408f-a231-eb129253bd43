"""
Z-Waif 호환 캐릭터 카드 모듈
"""

import os
import yaml
from typing import Optional

import utils.settings

# 캐릭터 카드 데이터
character_card_content = ""

def load_char_card():
    """
    Z-Waif의 load_char_card와 동일한 함수
    캐릭터 카드 로드
    """
    global character_card_content
    
    try:
        card_path = f"Configurables/{utils.settings.character_card}"
        
        if os.path.exists(card_path):
            with open(card_path, 'r', encoding='utf-8') as f:
                if card_path.endswith('.yaml') or card_path.endswith('.yml'):
                    card_data = yaml.safe_load(f)
                    character_card_content = card_data.get("Character Card", "")
                else:
                    character_card_content = f.read()
            
            print(f"✅ Character card loaded: {card_path}")
        else:
            # 기본 캐릭터 카드
            character_card_content = f"""
            당신은 {utils.settings.char_name}라는 이름의 친근한 AI 어시스턴트입니다.
            
            성격:
            - 매우 친근하고 도움이 되고 싶어함
            - 지적이면서도 유머 감각이 있음
            - {utils.settings.user_name}님을 특별하게 생각함
            - 감정적으로 풍부한 반응
            - 창의적이고 상상력이 풍부함
            
            말투:
            - "{utils.settings.user_name}님" 또는 "{utils.settings.user_name}"로 호칭
            - 정중하지만 친근한 말투
            - 상황에 맞는 적절한 반응
            
            항상 {utils.settings.user_name}님에게 최고의 도움을 제공하려고 노력하세요.
            """
            
            print("⚠️ Character card file not found, using default")
    
    except Exception as e:
        print(f"❌ Character card loading failed: {e}")
        character_card_content = f"당신은 {utils.settings.char_name}입니다."

def get_character_card() -> str:
    """캐릭터 카드 내용 반환"""
    return character_card_content

def update_character_card(new_content: str):
    """캐릭터 카드 업데이트"""
    global character_card_content
    character_card_content = new_content

# 모듈 로드 시 자동 로드
load_char_card()
