"""
Z-Waif 호환 Ollama API 모듈
"""

import requests
import json
from typing import Dict, Any, List

import utils.settings

def api_standard(history: List[Dict], temp_level: float = 0.7, stop: List[str] = None, max_tokens: int = 500) -> str:
    """
    Z-Waif의 api_standard와 동일한 함수
    Ollama API 표준 요청
    """
    try:
        # Ollama API 엔드포인트
        uri = f"{utils.settings.ollama_url if hasattr(utils.settings, 'ollama_url') else 'http://localhost:11434'}/api/generate"
        
        # 히스토리를 프롬프트로 변환
        prompt = ""
        for msg in history:
            role = msg.get("role", "")
            content = msg.get("content", "")
            
            if role == "system":
                prompt += f"System: {content}\n"
            elif role == "user":
                prompt += f"User: {content}\n"
            elif role == "assistant":
                prompt += f"Assistant: {content}\n"
        
        prompt += "Assistant: "
        
        request_data = {
            "model": utils.settings.zw_ollama_model,
            "prompt": prompt,
            "stream": False,
            "options": {
                "temperature": temp_level,
                "num_predict": max_tokens
            }
        }
        
        if stop:
            request_data["options"]["stop"] = stop
        
        response = requests.post(
            uri,
            json=request_data,
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            return result.get("response", "Ollama 응답 오류")
        else:
            return f"Ollama API 오류: {response.status_code}"
            
    except requests.exceptions.ConnectionError:
        return "Ollama 서버에 연결할 수 없습니다. 'ollama serve' 명령으로 서버를 시작해주세요."
    except Exception as e:
        return f"Ollama API 호출 오류: {str(e)}"

def api_visual(image_data: str, prompt: str) -> str:
    """비주얼 모델 API 요청"""
    try:
        uri = f"{utils.settings.ollama_url if hasattr(utils.settings, 'ollama_url') else 'http://localhost:11434'}/api/generate"
        
        request_data = {
            "model": utils.settings.zw_ollama_model_visual,
            "prompt": prompt,
            "images": [image_data],
            "stream": False
        }
        
        response = requests.post(uri, json=request_data, timeout=60)
        
        if response.status_code == 200:
            result = response.json()
            return result.get("response", "비주얼 모델 응답 오류")
        else:
            return f"비주얼 API 오류: {response.status_code}"
            
    except Exception as e:
        return f"비주얼 API 호출 오류: {str(e)}"
