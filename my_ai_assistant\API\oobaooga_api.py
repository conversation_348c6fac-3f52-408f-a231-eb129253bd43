"""
Z-Waif 호환 Oobabooga API 모듈
"""

import requests
import json
from typing import Dict, Any

import utils.settings

def api_standard(request: Dict[str, Any]) -> str:
    """
    Z-Waif의 api_standard와 동일한 함수
    Oobabooga API 표준 요청
    """
    try:
        uri = f"http://{utils.settings.host_port}/v1/chat/completions"
        
        response = requests.post(
            uri,
            json=request,
            headers={"Content-Type": "application/json"},
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            
            if 'choices' in result and len(result['choices']) > 0:
                return result['choices'][0]['message']['content']
            else:
                return "API 응답 형식 오류"
        else:
            return f"API 오류: {response.status_code}"
            
    except requests.exceptions.ConnectionError:
        return "Oobabooga 서버에 연결할 수 없습니다. 서버가 실행 중인지 확인해주세요."
    except Exception as e:
        return f"API 호출 오류: {str(e)}"

def api_streaming(request: Dict[str, Any]):
    """스트리밍 API 요청"""
    # 스트리밍 구현은 복잡하므로 기본 구현만
    return api_standard(request)
