"""
Z-Waif 호환 작업 프로필 모듈
"""

import json
import os
from typing import Dict, List

# 작업 프로필 데이터
task_profiles = {}

def load_task_profiles():
    """
    Z-Waif의 load_task_profiles와 동일한 함수
    작업 프로필 로드
    """
    global task_profiles
    
    try:
        profiles_path = "Configurables/TaskProfiles.json"
        
        if os.path.exists(profiles_path):
            with open(profiles_path, 'r', encoding='utf-8') as f:
                task_profiles = json.load(f)
            
            print(f"✅ Task profiles loaded: {len(task_profiles)} profiles")
        else:
            # 기본 작업 프로필
            task_profiles = {
                "Default": {
                    "name": "기본 모드",
                    "description": "일반적인 대화 모드",
                    "system_prompt": "친근하고 도움이 되는 AI 어시스턴트로 행동하세요."
                },
                "Creative": {
                    "name": "창작 모드", 
                    "description": "창의적인 작업을 위한 모드",
                    "system_prompt": "창의적이고 상상력이 풍부한 AI로 행동하세요."
                },
                "Professional": {
                    "name": "전문 모드",
                    "description": "전문적인 업무를 위한 모드", 
                    "system_prompt": "전문적이고 정확한 정보를 제공하는 AI로 행동하세요."
                }
            }
            
            print("⚠️ Task profiles file not found, using defaults")
    
    except Exception as e:
        print(f"❌ Task profiles loading failed: {e}")
        task_profiles = {}

def get_task_profile(profile_name: str) -> Dict:
    """작업 프로필 반환"""
    return task_profiles.get(profile_name, {})

def get_all_profiles() -> Dict:
    """모든 작업 프로필 반환"""
    return task_profiles

def add_task_profile(name: str, profile_data: Dict):
    """작업 프로필 추가"""
    task_profiles[name] = profile_data

# 모듈 로드 시 자동 로드
load_task_profiles()
