# 🤖 나만의 AI 어시스턴트

간단하고 사용하기 쉬운 AI 어시스턴트 시스템입니다. 텍스트와 음성으로 대화할 수 있으며, 웹 인터페이스를 통해 편리하게 사용할 수 있습니다.

## ✨ 주요 기능

- 💬 **자연어 대화**: 친근한 AI와 자유롭게 대화
- 🎤 **음성 인식**: Whisper를 사용한 정확한 음성 인식
- 🔊 **음성 합성**: TTS를 통한 자연스러운 음성 응답
- 🌐 **웹 인터페이스**: Gradio 기반의 직관적인 UI
- 💾 **대화 저장**: 중요한 대화를 JSON 파일로 저장
- 🧠 **컨텍스트 기억**: 이전 대화 내용을 기억하여 자연스러운 대화

## 🔧 시스템 요구사항

- **Python**: 3.8 이상
- **운영체제**: Windows, macOS, Linux
- **메모리**: 최소 4GB RAM (8GB 권장)
- **저장공간**: 최소 2GB 여유 공간

## 📦 설치 방법

### 1단계: 저장소 클론 또는 다운로드
```bash
# Git 사용시
git clone <repository-url>
cd my_ai_assistant

# 또는 파일을 직접 다운로드하여 폴더에 저장
```

### 2단계: 가상환경 생성 (권장)
```bash
# 가상환경 생성
python -m venv venv

# 가상환경 활성화
# Windows:
venv\Scripts\activate

# macOS/Linux:
source venv/bin/activate
```

### 3단계: 의존성 설치
```bash
pip install -r requirements.txt
```

### 4단계: Ollama 설치 및 설정
```bash
# Ollama 설치 (https://ollama.ai)
# Windows/Mac: 공식 웹사이트에서 설치 프로그램 다운로드
# Linux:
curl -fsSL https://ollama.ai/install.sh | sh

# 모델 다운로드
ollama pull llama2
```

## 🚀 실행 방법

### 기본 실행
```bash
python main.py
```

### 환경 변수 설정 (선택사항)
`.env` 파일을 수정하여 설정을 변경할 수 있습니다:

```env
AI_NAME=Luna              # AI 이름
USER_NAME=사용자          # 사용자 호칭
OLLAMA_MODEL=llama2       # 사용할 Ollama 모델
WHISPER_MODEL=base        # Whisper 모델 크기
```

## 🎯 사용법

### 웹 인터페이스 접속
1. 프로그램 실행 후 자동으로 브라우저가 열립니다
2. 수동 접속: http://localhost:7860

### 텍스트 채팅
1. 하단 입력창에 메시지 입력
2. "전송" 버튼 클릭 또는 Enter 키

### 음성 채팅
1. "음성 입력" 섹션에서 마이크 버튼 클릭
2. 말하기 완료 후 자동으로 텍스트 변환 및 응답

### 대화 저장
1. "대화 저장" 버튼 클릭
2. `conversations/` 폴더에 JSON 파일로 저장

## ⚙️ 고급 설정

### OpenAI API 사용 (선택사항)
```bash
# .env 파일에 API 키 추가
OPENAI_API_KEY=your_api_key_here
```

### 음성 설정 조정
```python
# main.py에서 TTS 설정 수정
self.tts_engine.setProperty('rate', 200)    # 말하기 속도
self.tts_engine.setProperty('volume', 0.9)  # 볼륨
```

### 캐릭터 성격 변경
`main.py`의 `load_personality()` 함수에서 AI의 성격을 수정할 수 있습니다.

## 🔧 문제 해결

### 일반적인 문제들

#### 1. Ollama 연결 오류
```bash
# Ollama 서비스 상태 확인
ollama list

# Ollama 서버 재시작
ollama serve
```

#### 2. 음성 인식 오류
- 마이크 권한 확인
- 오디오 드라이버 업데이트
- 다른 Whisper 모델 시도 (`tiny`, `small`, `medium`)

#### 3. 음성 합성 오류
```bash
# Windows에서 TTS 엔진 확인
pip install --upgrade pyttsx3

# macOS에서 권한 확인
# 시스템 환경설정 > 보안 및 개인정보보호 > 마이크
```

#### 4. 패키지 설치 오류
```bash
# pip 업그레이드
pip install --upgrade pip

# 개별 패키지 설치
pip install gradio whisper pyttsx3 requests
```

## 📁 프로젝트 구조

```
my_ai_assistant/
├── main.py              # 메인 실행 파일
├── requirements.txt     # 의존성 목록
├── .env                # 환경 설정
├── README.md           # 이 파일
└── conversations/      # 저장된 대화 (자동 생성)
    └── conversation_YYYYMMDD_HHMMSS.json
```

## 🎨 커스터마이징

### AI 이름과 성격 변경
```python
# main.py에서 수정
self.name = "원하는_이름"
self.personality = "원하는_성격_설정"
```

### UI 테마 변경
```python
# Gradio 테마 변경
theme=gr.themes.Glass()  # 또는 Monochrome(), Soft() 등
```

### 추가 기능 개발
- Discord 봇 연동
- 이미지 인식 기능
- RAG 기반 장기 기억
- 음성 클로닝
- VTube Studio 연동

## 📝 라이선스

이 프로젝트는 MIT 라이선스 하에 배포됩니다.

## 🤝 기여하기

버그 리포트, 기능 제안, 코드 기여를 환영합니다!

## 📞 지원

문제가 발생하면 다음을 확인해주세요:
1. Python 버전 (3.8 이상)
2. 모든 의존성 설치 완료
3. Ollama 서버 실행 상태
4. 마이크/스피커 권한

---

**즐거운 AI 어시스턴트 경험을 즐기세요! 🚀✨**
