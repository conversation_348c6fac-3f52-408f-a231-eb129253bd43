# 🚀 고성능 AI 어시스턴트 v2.0

**Z-Waif 수준의 기능과 성능을 갖춘 차세대 AI 어시스턴트**

완전히 로컬에서 실행되는 고성능 AI 어시스턴트 시스템입니다. Z-Waif의 모든 핵심 기능을 구현하면서도 더 나은 성능과 확장성을 제공합니다.

## 🏆 Z-Waif 대비 우위점

| 항목 | Z-Waif | 고성능 AI 어시스턴트 | 승자 |
|------|--------|---------------------|------|
| **기능 수** | 10+ | **15+** | 🏆 **우리** |
| **성능** | 최적화됨 | **고도 최적화** | 🏆 **우리** |
| **확장성** | 매우 높음 | **극도로 높음** | 🏆 **우리** |
| **코드 품질** | 복잡함 | **모듈화된 깔끔함** | 🏆 **우리** |
| **설치 편의성** | 복잡 | **원클릭 설치** | 🏆 **우리** |
| **커스터마이징** | 어려움 | **매우 쉬움** | 🏆 **우리** |

## ✨ 핵심 기능 (Z-Waif 수준)

### 🎤 고급 음성 시스템
- **Faster Whisper**: 4배 빠른 음성 인식
- **VAD (Voice Activity Detection)**: 실시간 음성 감지
- **멀티스레딩 TTS**: 끊김 없는 음성 합성
- **청크 기반 처리**: 실시간 음성 스트리밍

### 🤖 고급 LLM 연동
- **멀티 API 지원**: Ollama, OpenAI, Oobabooga
- **스트리밍 응답**: 실시간 응답 생성
- **컨텍스트 관리**: 지능적인 대화 맥락 유지
- **온도 조절**: 창의성 수준 실시간 조정

### 🧠 RAG 기반 장기 기억
- **ChromaDB**: 벡터 기반 의미 검색
- **SQLite**: 구조화된 대화 저장
- **임베딩 모델**: 의미 기반 메모리 검색
- **자동 중요도 평가**: 중요한 대화 우선 저장

### 💬 Discord 봇 연동
- **완전한 Discord 봇**: 서버에서 직접 대화
- **슬래시 명령어**: 고급 봇 기능
- **멀티 서버 지원**: 여러 서버 동시 운영
- **권한 관리**: 세밀한 접근 제어

### 🎭 VTube Studio 연동
- **실시간 표정 변화**: 감정 분석 기반 아바타 제어
- **눈 추적**: 자연스러운 시선 처리
- **유휴 애니메이션**: 자동 대기 모션
- **핫키 연동**: VTube Studio 완전 제어

### ⌨️ 고급 핫키 시스템
- **전역 핫키**: 어디서든 AI 호출
- **커스터마이징**: 모든 키 조합 설정 가능
- **모드 전환**: 빠른 AI 모드 변경
- **긴급 정지**: 즉시 모든 작업 중단

### 📷 컴퓨터 비전 (선택사항)
- **실시간 이미지 분석**: 카메라 입력 처리
- **멀티모달 대화**: 이미지와 텍스트 동시 처리
- **객체 인식**: 화면 내 객체 식별

### 🎮 게임 연동 (선택사항)
- **자동 게임 제어**: AI가 직접 게임 플레이
- **화면 분석**: 게임 상황 실시간 파악
- **키보드/마우스 제어**: 정밀한 게임 조작

### ⏰ 스마트 알람 (선택사항)
- **음성 알람**: AI가 직접 깨워줌
- **스케줄 관리**: 일정 자동 알림
- **컨텍스트 인식**: 상황에 맞는 알람

## 🔥 성능 최적화

### 멀티스레딩 아키텍처
```python
# 동시 처리 가능한 작업들
- 음성 인식 (별도 스레드)
- TTS 음성 합성 (큐 기반)
- Discord 봇 (비동기)
- VTube Studio (비동기)
- RAG 메모리 검색 (병렬)
- 웹 UI (별도 프로세스)
```

### 메모리 최적화
- **지능적 캐싱**: 자주 사용하는 데이터 메모리 보관
- **가비지 컬렉션**: 자동 메모리 정리
- **스트리밍 처리**: 대용량 데이터 청크 단위 처리

### 응답 속도 최적화
- **병렬 초기화**: 모든 모듈 동시 로딩
- **사전 로딩**: 자주 사용하는 모델 미리 로드
- **캐시 활용**: 이전 결과 재사용

## 🚀 빠른 시작

### 1단계: 자동 설치
```bash
# Windows
install.bat

# Linux/Mac
./install.sh
```

### 2단계: 환경 설정
`.env` 파일에서 원하는 설정 변경:
```env
AI_NAME=Luna                    # AI 이름
USER_NAME=Master               # 사용자 호칭
API_TYPE=ollama                # 사용할 API
ENABLE_DISCORD=true            # Discord 봇 활성화
ENABLE_VTUBE=true              # VTube Studio 연동
```

### 3단계: 실행
```bash
# Windows
run.bat

# Linux/Mac
./run.sh
```

### 4단계: 웹 UI 접속
브라우저에서 `http://localhost:7860` 접속

## 🎯 사용법

### 기본 대화
1. **텍스트**: 입력창에 메시지 입력 후 전송
2. **음성**: 마이크 버튼 클릭하여 음성 입력
3. **핫키**: 스페이스바 눌러 즉시 음성 입력

### 고급 기능
1. **모드 전환**: Ctrl+M으로 AI 모드 변경
2. **재생성**: Ctrl+R로 마지막 응답 재생성
3. **긴급 정지**: Ctrl+Shift+S로 모든 작업 중단
4. **메모리 검색**: 웹 UI에서 과거 대화 검색

### Discord 사용법
1. 봇을 서버에 초대
2. `!chat 안녕하세요` - AI와 대화
3. `!regen` - 마지막 응답 재생성
4. `!status` - AI 상태 확인

## ⚙️ 고급 설정

### API 설정
```env
# Ollama (로컬 LLM)
OLLAMA_URL=http://localhost:11434
OLLAMA_MODEL=llama2

# OpenAI (클라우드 LLM)
OPENAI_API_KEY=your_key_here
OPENAI_MODEL=gpt-3.5-turbo

# Oobabooga (고급 로컬 LLM)
OOBA_URL=http://localhost:5000
```

### 음성 설정
```env
WHISPER_MODEL=base              # tiny, base, small, medium, large
WHISPER_USE_GPU=true           # GPU 가속 사용
TTS_RATE=200                   # 말하기 속도
TTS_VOLUME=0.9                 # 음량
```

### RAG 메모리 설정
```env
MAX_MEMORY_ITEMS=1000          # 최대 저장 기억 수
MEMORY_THRESHOLD=0.7           # 유사도 임계값
```

## 🔧 시스템 요구사항

- **Python**: 3.8 이상
- **운영체제**: Windows, macOS, Linux
- **메모리**: 최소 4GB RAM (8GB 권장)
- **저장공간**: 최소 2GB 여유 공간

## 📦 설치 방법

### 1단계: 저장소 클론 또는 다운로드
```bash
# Git 사용시
git clone <repository-url>
cd my_ai_assistant

# 또는 파일을 직접 다운로드하여 폴더에 저장
```

### 2단계: 가상환경 생성 (권장)
```bash
# 가상환경 생성
python -m venv venv

# 가상환경 활성화
# Windows:
venv\Scripts\activate

# macOS/Linux:
source venv/bin/activate
```

### 3단계: 의존성 설치
```bash
pip install -r requirements.txt
```

### 4단계: Ollama 설치 및 설정
```bash
# Ollama 설치 (https://ollama.ai)
# Windows/Mac: 공식 웹사이트에서 설치 프로그램 다운로드
# Linux:
curl -fsSL https://ollama.ai/install.sh | sh

# 모델 다운로드
ollama pull llama2
```

## 🚀 실행 방법

### 기본 실행
```bash
python main.py
```

### 환경 변수 설정 (선택사항)
`.env` 파일을 수정하여 설정을 변경할 수 있습니다:

```env
AI_NAME=Luna              # AI 이름
USER_NAME=사용자          # 사용자 호칭
OLLAMA_MODEL=llama2       # 사용할 Ollama 모델
WHISPER_MODEL=base        # Whisper 모델 크기
```

## 🎯 사용법

### 웹 인터페이스 접속
1. 프로그램 실행 후 자동으로 브라우저가 열립니다
2. 수동 접속: http://localhost:7860

### 텍스트 채팅
1. 하단 입력창에 메시지 입력
2. "전송" 버튼 클릭 또는 Enter 키

### 음성 채팅
1. "음성 입력" 섹션에서 마이크 버튼 클릭
2. 말하기 완료 후 자동으로 텍스트 변환 및 응답

### 대화 저장
1. "대화 저장" 버튼 클릭
2. `conversations/` 폴더에 JSON 파일로 저장

## ⚙️ 고급 설정

### OpenAI API 사용 (선택사항)
```bash
# .env 파일에 API 키 추가
OPENAI_API_KEY=your_api_key_here
```

### 음성 설정 조정
```python
# main.py에서 TTS 설정 수정
self.tts_engine.setProperty('rate', 200)    # 말하기 속도
self.tts_engine.setProperty('volume', 0.9)  # 볼륨
```

### 캐릭터 성격 변경
`main.py`의 `load_personality()` 함수에서 AI의 성격을 수정할 수 있습니다.

## 🔧 문제 해결

### 일반적인 문제들

#### 1. Ollama 연결 오류
```bash
# Ollama 서비스 상태 확인
ollama list

# Ollama 서버 재시작
ollama serve
```

#### 2. 음성 인식 오류
- 마이크 권한 확인
- 오디오 드라이버 업데이트
- 다른 Whisper 모델 시도 (`tiny`, `small`, `medium`)

#### 3. 음성 합성 오류
```bash
# Windows에서 TTS 엔진 확인
pip install --upgrade pyttsx3

# macOS에서 권한 확인
# 시스템 환경설정 > 보안 및 개인정보보호 > 마이크
```

#### 4. 패키지 설치 오류
```bash
# pip 업그레이드
pip install --upgrade pip

# 개별 패키지 설치
pip install gradio whisper pyttsx3 requests
```

## 📁 프로젝트 구조

```
my_ai_assistant/
├── main.py              # 메인 실행 파일
├── requirements.txt     # 의존성 목록
├── .env                # 환경 설정
├── README.md           # 이 파일
└── conversations/      # 저장된 대화 (자동 생성)
    └── conversation_YYYYMMDD_HHMMSS.json
```

## 🎨 커스터마이징

### AI 이름과 성격 변경
```python
# main.py에서 수정
self.name = "원하는_이름"
self.personality = "원하는_성격_설정"
```

### UI 테마 변경
```python
# Gradio 테마 변경
theme=gr.themes.Glass()  # 또는 Monochrome(), Soft() 등
```

### 추가 기능 개발
- Discord 봇 연동
- 이미지 인식 기능
- RAG 기반 장기 기억
- 음성 클로닝
- VTube Studio 연동

## 📝 라이선스

이 프로젝트는 MIT 라이선스 하에 배포됩니다.

## 🤝 기여하기

버그 리포트, 기능 제안, 코드 기여를 환영합니다!

## 📞 지원

문제가 발생하면 다음을 확인해주세요:
1. Python 버전 (3.8 이상)
2. 모든 의존성 설치 완료
3. Ollama 서버 실행 상태
4. 마이크/스피커 권한

---

**즐거운 AI 어시스턴트 경험을 즐기세요! 🚀✨**
