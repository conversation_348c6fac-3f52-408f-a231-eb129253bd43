@echo off
echo ========================================
echo  고성능 AI 어시스턴트 설치 스크립트 v2.0
echo    (Z-Waif 수준의 기능과 성능)
echo ========================================
echo.

REM 현재 디렉토리 설정
set "SCRIPT_DIR=%~dp0"
cd /d "%SCRIPT_DIR%"

echo [1/5] Python 버전 확인 중...
python --version
if %errorlevel% neq 0 (
    echo ❌ Python이 설치되지 않았습니다!
    echo Python 3.8 이상을 설치해주세요: https://python.org
    pause
    exit /b 1
)

echo.
echo [2/5] 가상환경 생성 중...
if exist venv (
    echo 기존 가상환경을 삭제합니다...
    rmdir /s /q venv
)

python -m venv venv
if %errorlevel% neq 0 (
    echo ❌ 가상환경 생성 실패!
    pause
    exit /b 1
)

echo.
echo [3/5] 가상환경 활성화 중...
call venv\Scripts\activate

echo.
echo [4/5] 의존성 설치 중...
echo 이 과정은 몇 분이 걸릴 수 있습니다...
python -m pip install --upgrade pip
pip install -r requirements.txt

if %errorlevel% neq 0 (
    echo ❌ 의존성 설치 실패!
    echo 인터넷 연결을 확인하고 다시 시도해주세요.
    pause
    exit /b 1
)

echo.
echo [5/5] Ollama 설치 확인...
echo Ollama가 설치되어 있는지 확인합니다...
ollama --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ⚠️  Ollama가 설치되지 않았습니다!
    echo.
    echo Ollama 설치 방법:
    echo 1. https://ollama.ai 방문
    echo 2. Windows용 설치 프로그램 다운로드
    echo 3. 설치 후 다음 명령어 실행:
    echo    ollama pull llama2
    echo.
    echo Ollama 설치 후 run.bat을 실행해주세요.
) else (
    echo ✅ Ollama가 설치되어 있습니다!
    echo.
    echo 기본 모델 다운로드 중...
    ollama pull llama2
    if %errorlevel% neq 0 (
        echo ⚠️  모델 다운로드 실패. 수동으로 다운로드해주세요:
        echo ollama pull llama2
    )
)

echo.
echo ========================================
echo           설치 완료! 🎉
echo ========================================
echo.
echo 다음 단계:
echo 1. run.bat을 실행하여 AI 어시스턴트 시작
echo 2. 또는 수동 실행: python main.py
echo.
echo 문제가 발생하면 README.md를 참고해주세요.
echo.
pause
