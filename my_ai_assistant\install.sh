#!/bin/bash

echo "========================================"
echo "   나만의 AI 어시스턴트 설치 스크립트"
echo "========================================"
echo

# 현재 디렉토리로 이동
cd "$(dirname "$0")"

echo "[1/5] Python 버전 확인 중..."
if ! command -v python3 &> /dev/null; then
    echo "❌ Python3이 설치되지 않았습니다!"
    echo "Python 3.8 이상을 설치해주세요."
    exit 1
fi

python3 --version

echo
echo "[2/5] 가상환경 생성 중..."
if [ -d "venv" ]; then
    echo "기존 가상환경을 삭제합니다..."
    rm -rf venv
fi

python3 -m venv venv
if [ $? -ne 0 ]; then
    echo "❌ 가상환경 생성 실패!"
    exit 1
fi

echo
echo "[3/5] 가상환경 활성화 중..."
source venv/bin/activate

echo
echo "[4/5] 의존성 설치 중..."
echo "이 과정은 몇 분이 걸릴 수 있습니다..."
python -m pip install --upgrade pip
pip install -r requirements.txt

if [ $? -ne 0 ]; then
    echo "❌ 의존성 설치 실패!"
    echo "인터넷 연결을 확인하고 다시 시도해주세요."
    exit 1
fi

echo
echo "[5/5] Ollama 설치 확인..."
if ! command -v ollama &> /dev/null; then
    echo "⚠️  Ollama가 설치되지 않았습니다!"
    echo
    echo "Ollama 설치 방법:"
    echo "curl -fsSL https://ollama.ai/install.sh | sh"
    echo
    echo "설치 후 다음 명령어 실행:"
    echo "ollama pull llama2"
    echo
    echo "Ollama 설치 후 ./run.sh를 실행해주세요."
else
    echo "✅ Ollama가 설치되어 있습니다!"
    echo
    echo "기본 모델 다운로드 중..."
    ollama pull llama2
    if [ $? -ne 0 ]; then
        echo "⚠️  모델 다운로드 실패. 수동으로 다운로드해주세요:"
        echo "ollama pull llama2"
    fi
fi

echo
echo "========================================"
echo "           설치 완료! 🎉"
echo "========================================"
echo
echo "다음 단계:"
echo "1. ./run.sh를 실행하여 AI 어시스턴트 시작"
echo "2. 또는 수동 실행: python main.py"
echo
echo "문제가 발생하면 README.md를 참고해주세요."
echo
