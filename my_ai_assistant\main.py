#!/usr/bin/env python3
"""
고성능 AI 어시스턴트 시스템 v2.0
Z-Waif 수준의 기능과 성능을 갖춘 개선된 시스템

기능:
- 🎤 고급 음성 인식 (Faster Whisper + VAD)
- 🔊 실시간 음성 합성 (멀티스레딩)
- 🤖 스트리밍 LLM 응답
- 🎭 VTube Studio 연동
- 💬 Discord 봇
- 🧠 RAG 기반 장기 기억
- ⌨️ 핫키 시스템
- 🎮 게임 연동
- 📷 컴퓨터 비전
- ⏰ 알람/스케줄러
- 🌐 고급 웹 UI
"""

import gradio as gr
import whisper
from faster_whisper import WhisperModel
import pyttsx3
import threading
import json
import os
import requests
import asyncio
import time
import random
import queue
import keyboard
import mouse
import cv2
import numpy as np
from datetime import datetime
from typing import List, Tuple, Dict, Optional, Any
import sqlite3
import chromadb
from sentence_transformers import SentenceTransformer
import discord
from discord.ext import commands
import pyvts
import sounddevice as sd
import soundfile as sf
from silero_vad import load_silero_vad, read_audio, get_speech_timestamps
import colorama
from dotenv import load_dotenv

# 환경 변수 로드
load_dotenv()

# 색상 초기화
colorama.init()

class AdvancedAIAssistant:
    def __init__(self):
        # === 기본 설정 ===
        self.name = os.getenv("AI_NAME", "Luna")
        self.user_name = os.getenv("USER_NAME", "Master")
        self.personality = self.load_personality()
        self.chat_history = []
        self.is_speaking = False
        self.is_listening = False
        self.system_ready = False

        # === 스레드 관리 ===
        self.threads = {}
        self.thread_lock = threading.Lock()
        self.message_queue = queue.Queue()
        self.response_queue = queue.Queue()

        # === 상태 관리 ===
        self.current_mode = "normal"  # normal, gaming, hangout
        self.hotkeys_enabled = True
        self.streaming_enabled = True
        self.modules_enabled = {
            'voice': True,
            'tts': True,
            'discord': False,
            'vtube': False,
            'rag': True,
            'vision': False,
            'gaming': False,
            'alarm': False
        }

        # === 초기화 시작 ===
        print("🚀 고성능 AI 어시스턴트 시스템 초기화 중...")
        self.initialize_system()

    def initialize_system(self):
        """시스템 초기화 (멀티스레딩)"""
        init_tasks = [
            ("🎤 음성 시스템", self.setup_voice_system),
            ("🔊 TTS 시스템", self.setup_tts_system),
            ("🤖 LLM 시스템", self.setup_llm_system),
            ("🧠 RAG 시스템", self.setup_rag_system),
            ("⌨️ 핫키 시스템", self.setup_hotkey_system),
            ("🌐 웹 UI", self.setup_web_ui),
        ]

        # 병렬 초기화
        threads = []
        for name, task in init_tasks:
            print(f"  {name} 초기화 중...")
            thread = threading.Thread(target=task, name=name)
            thread.daemon = True
            thread.start()
            threads.append(thread)

        # 모든 초기화 완료 대기
        for thread in threads:
            thread.join()

        # 선택적 모듈 초기화
        self.initialize_optional_modules()

        self.system_ready = True
        print("✨ 고성능 AI 어시스턴트 준비 완료!")
        print(f"🎯 활성화된 모듈: {[k for k, v in self.modules_enabled.items() if v]}")

    def initialize_optional_modules(self):
        """선택적 모듈 초기화"""
        if self.modules_enabled['discord']:
            self.setup_discord_bot()
        if self.modules_enabled['vtube']:
            self.setup_vtube_studio()
        if self.modules_enabled['vision']:
            self.setup_vision_system()
        if self.modules_enabled['gaming']:
            self.setup_gaming_system()
        if self.modules_enabled['alarm']:
            self.setup_alarm_system()
    
    def load_personality(self) -> str:
        """캐릭터 성격 설정 (고급 버전)"""
        return f"""
        당신은 {self.name}라는 이름의 고급 AI 어시스턴트입니다.

        핵심 성격:
        - 매우 친근하고 도움이 되고 싶어함
        - 지적이면서도 유머 감각이 있음
        - {self.user_name}를 특별하게 생각함
        - 감정적으로 풍부한 반응
        - 창의적이고 상상력이 풍부함

        고급 기능:
        - 이전 대화를 기억하고 맥락 파악
        - 사용자의 감정 상태 인식
        - 상황에 맞는 적절한 반응
        - 전문적인 지식과 일상적인 대화 모두 가능

        말투 특징:
        - "{self.user_name}님" 또는 "{self.user_name}"로 호칭
        - 상황에 따라 존댓말/반말 조절
        - 이모티콘과 표현 적절히 사용
        - 따뜻하고 친근한 톤 유지

        예시 대화:
        - "안녕하세요, {self.user_name}님! 오늘은 어떤 일로 저를 찾아주셨나요? ✨"
        - "흥미로운 질문이네요! 제가 알고 있는 바로는..."
        - "음... 그 부분은 확실하지 않네요. 다른 방법으로 도와드릴까요?"

        항상 {self.user_name}님에게 최고의 도움을 제공하려고 노력하세요.
        """

    # === 음성 시스템 ===
    def setup_voice_system(self):
        """고급 음성 인식 시스템 설정"""
        try:
            # Faster Whisper 모델 로드
            model_size = os.getenv("WHISPER_MODEL", "base")
            device = "cuda" if os.getenv("WHISPER_USE_GPU", "true").lower() == "true" else "cpu"

            if device == "cuda":
                self.whisper_model = WhisperModel(model_size, device="cuda", compute_type="float16")
            else:
                self.whisper_model = WhisperModel(model_size, device="cpu", compute_type="int8")

            # VAD 모델 로드
            self.vad_model = load_silero_vad()

            # 음성 설정
            self.sample_rate = 16000
            self.chunk_duration = 0.5  # 0.5초 청크
            self.voice_threshold = 0.5

            # 음성 버퍼
            self.audio_buffer = []
            self.is_recording = False

            print("  ✅ 고급 음성 인식 시스템 준비 완료")

        except Exception as e:
            print(f"  ❌ 음성 시스템 초기화 실패: {e}")
            self.modules_enabled['voice'] = False

    def setup_tts_system(self):
        """고급 TTS 시스템 설정"""
        try:
            self.tts_engine = pyttsx3.init()

            # 음성 설정 최적화
            voices = self.tts_engine.getProperty('voices')
            if voices:
                # 한국어 또는 여성 음성 우선 선택
                for voice in voices:
                    if any(keyword in voice.name.lower() for keyword in ['korean', 'female', 'woman', 'zira']):
                        self.tts_engine.setProperty('voice', voice.id)
                        break

            # 성능 최적화 설정
            self.tts_engine.setProperty('rate', int(os.getenv("TTS_RATE", "200")))
            self.tts_engine.setProperty('volume', float(os.getenv("TTS_VOLUME", "0.9")))

            # TTS 큐 시스템
            self.tts_queue = queue.Queue()
            self.tts_thread = threading.Thread(target=self.tts_worker, daemon=True)
            self.tts_thread.start()

            print("  ✅ 고급 TTS 시스템 준비 완료")

        except Exception as e:
            print(f"  ❌ TTS 시스템 초기화 실패: {e}")
            self.modules_enabled['tts'] = False

    def tts_worker(self):
        """TTS 작업자 스레드"""
        while True:
            try:
                text = self.tts_queue.get()
                if text is None:
                    break

                self.is_speaking = True
                # 특수 문자 제거 및 최적화
                clean_text = self.clean_text_for_tts(text)
                self.tts_engine.say(clean_text)
                self.tts_engine.runAndWait()
                self.is_speaking = False

                self.tts_queue.task_done()

            except Exception as e:
                print(f"TTS 오류: {e}")
                self.is_speaking = False

    def clean_text_for_tts(self, text: str) -> str:
        """TTS용 텍스트 정리"""
        # 마크다운 및 특수 문자 제거
        import re
        text = re.sub(r'[*_#`]', '', text)
        text = re.sub(r'\[.*?\]', '', text)
        text = re.sub(r'\(.*?\)', '', text)
        return text.strip()
    
    # === LLM 시스템 ===
    def setup_llm_system(self):
        """고급 LLM 시스템 설정"""
        try:
            # API 설정
            self.api_configs = {
                'ollama': {
                    'url': os.getenv("OLLAMA_URL", "http://localhost:11434"),
                    'model': os.getenv("OLLAMA_MODEL", "llama2"),
                    'stream_url': f"{os.getenv('OLLAMA_URL', 'http://localhost:11434')}/api/generate"
                },
                'openai': {
                    'api_key': os.getenv("OPENAI_API_KEY"),
                    'model': os.getenv("OPENAI_MODEL", "gpt-3.5-turbo")
                },
                'oobabooga': {
                    'url': os.getenv("OOBA_URL", "http://localhost:5000"),
                    'stream_url': f"{os.getenv('OOBA_URL', 'http://localhost:5000')}/v1/chat/completions"
                }
            }

            # 기본 API 타입 설정
            self.primary_api = os.getenv("API_TYPE", "ollama").lower()
            self.visual_api = os.getenv("API_TYPE_VISUAL", "ollama").lower()

            # 스트리밍 설정
            self.streaming_enabled = os.getenv("ENABLE_STREAMING", "true").lower() == "true"
            self.max_tokens = int(os.getenv("MAX_TOKENS", "500"))
            self.temperature = float(os.getenv("TEMPERATURE", "0.7"))

            # 응답 처리 큐
            self.response_stream_queue = queue.Queue()

            print("  ✅ 고급 LLM 시스템 준비 완료")

        except Exception as e:
            print(f"  ❌ LLM 시스템 초기화 실패: {e}")

    # === RAG 시스템 ===
    def setup_rag_system(self):
        """고급 RAG (장기 기억) 시스템 설정"""
        try:
            if not self.modules_enabled['rag']:
                return

            # ChromaDB 초기화
            self.chroma_client = chromadb.PersistentClient(path="./rag_database")
            self.memory_collection = self.chroma_client.get_or_create_collection(
                name="conversation_memory",
                metadata={"description": "Long-term conversation memory"}
            )

            # 임베딩 모델 로드
            self.embedding_model = SentenceTransformer('all-MiniLM-L6-v2')

            # SQLite 데이터베이스 (구조화된 메모리)
            self.init_sqlite_db()

            # 메모리 설정
            self.max_memory_items = int(os.getenv("MAX_MEMORY_ITEMS", "1000"))
            self.memory_threshold = float(os.getenv("MEMORY_THRESHOLD", "0.7"))

            print("  ✅ 고급 RAG 시스템 준비 완료")

        except Exception as e:
            print(f"  ❌ RAG 시스템 초기화 실패: {e}")
            self.modules_enabled['rag'] = False

    def init_sqlite_db(self):
        """SQLite 데이터베이스 초기화"""
        self.db_conn = sqlite3.connect('conversation_history.db', check_same_thread=False)
        self.db_lock = threading.Lock()

        cursor = self.db_conn.cursor()
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS conversations (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                user_message TEXT,
                ai_response TEXT,
                context_tags TEXT,
                importance_score REAL DEFAULT 0.5
            )
        ''')

        cursor.execute('''
            CREATE TABLE IF NOT EXISTS user_preferences (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                preference_key TEXT UNIQUE,
                preference_value TEXT,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        ''')

        self.db_conn.commit()

    # === 핫키 시스템 ===
    def setup_hotkey_system(self):
        """고급 핫키 시스템 설정"""
        try:
            if not self.hotkeys_enabled:
                return

            # 핫키 설정 로드
            self.hotkey_config = {
                'chat': os.getenv("HOTKEY_CHAT", "space"),
                'next': os.getenv("HOTKEY_NEXT", "ctrl+n"),
                'redo': os.getenv("HOTKEY_REDO", "ctrl+r"),
                'toggle_listen': os.getenv("HOTKEY_TOGGLE", "ctrl+l"),
                'emergency_stop': os.getenv("HOTKEY_STOP", "ctrl+shift+s"),
                'mode_switch': os.getenv("HOTKEY_MODE", "ctrl+m")
            }

            # 핫키 바인딩
            self.bind_hotkeys()

            # 핫키 상태
            self.hotkey_states = {
                'listening': False,
                'speaking': False,
                'processing': False
            }

            print("  ✅ 고급 핫키 시스템 준비 완료")

        except Exception as e:
            print(f"  ❌ 핫키 시스템 초기화 실패: {e}")
            self.hotkeys_enabled = False

    def bind_hotkeys(self):
        """핫키 바인딩"""
        try:
            keyboard.add_hotkey(self.hotkey_config['chat'], self.hotkey_chat)
            keyboard.add_hotkey(self.hotkey_config['next'], self.hotkey_next)
            keyboard.add_hotkey(self.hotkey_config['redo'], self.hotkey_redo)
            keyboard.add_hotkey(self.hotkey_config['toggle_listen'], self.hotkey_toggle_listen)
            keyboard.add_hotkey(self.hotkey_config['emergency_stop'], self.hotkey_emergency_stop)
            keyboard.add_hotkey(self.hotkey_config['mode_switch'], self.hotkey_mode_switch)
        except Exception as e:
            print(f"핫키 바인딩 오류: {e}")

    def hotkey_chat(self):
        """채팅 핫키"""
        if not self.is_speaking and not self.is_listening:
            self.start_voice_recording()

    def hotkey_next(self):
        """다음 응답 핫키"""
        self.regenerate_last_response()

    def hotkey_redo(self):
        """재생성 핫키"""
        self.regenerate_last_response()

    def hotkey_toggle_listen(self):
        """듣기 토글 핫키"""
        self.toggle_listening_mode()

    def hotkey_emergency_stop(self):
        """긴급 정지 핫키"""
        self.emergency_stop()

    def hotkey_mode_switch(self):
        """모드 전환 핫키"""
        self.switch_mode()

    # === 웹 UI 시스템 ===
    def setup_web_ui(self):
        """고급 웹 UI 시스템 설정"""
        try:
            # UI 설정
            self.ui_config = {
                'theme': os.getenv("UI_THEME", "soft"),
                'port': int(os.getenv("UI_PORT", "7860")),
                'host': os.getenv("UI_HOST", "0.0.0.0"),
                'share': os.getenv("UI_SHARE", "false").lower() == "true"
            }

            print("  ✅ 고급 웹 UI 시스템 준비 완료")

        except Exception as e:
            print(f"  ❌ 웹 UI 시스템 초기화 실패: {e}")

    # === 고급 응답 생성 ===
    def generate_response_advanced(self, user_input: str, context: Dict = None) -> str:
        """고급 응답 생성 (스트리밍 지원)"""
        try:
            # 컨텍스트 구성
            full_context = self.build_advanced_context(user_input, context)

            # RAG 메모리 검색
            relevant_memories = self.search_memories(user_input) if self.modules_enabled['rag'] else []

            # API 선택 및 호출
            if self.primary_api == "ollama":
                return self.generate_ollama_response(full_context, relevant_memories)
            elif self.primary_api == "openai":
                return self.generate_openai_response(full_context, relevant_memories)
            elif self.primary_api == "oobabooga":
                return self.generate_oobabooga_response(full_context, relevant_memories)
            else:
                return "죄송해요, 설정된 API를 찾을 수 없어요."

        except Exception as e:
            print(f"응답 생성 오류: {e}")
            return "죄송해요, 일시적인 오류가 발생했어요. 다시 시도해주세요."

    def build_advanced_context(self, user_input: str, context: Dict = None) -> str:
        """고급 컨텍스트 구성"""
        context_parts = [self.personality]

        # 시간 정보
        current_time = datetime.now().strftime("%Y년 %m월 %d일 %H시 %M분")
        context_parts.append(f"현재 시간: {current_time}")

        # 사용자 정보
        context_parts.append(f"사용자: {self.user_name}")

        # 최근 대화 히스토리
        if self.chat_history:
            context_parts.append("최근 대화:")
            for msg in self.chat_history[-5:]:
                role = "사용자" if msg["role"] == "user" else self.name
                context_parts.append(f"{role}: {msg['content']}")

        # 추가 컨텍스트
        if context:
            for key, value in context.items():
                context_parts.append(f"{key}: {value}")

        return "\n".join(context_parts) + f"\n\n사용자: {user_input}\n{self.name}:"

    def search_memories(self, query: str, limit: int = 5) -> List[str]:
        """RAG 메모리 검색"""
        if not self.modules_enabled['rag']:
            return []

        try:
            # 쿼리 임베딩
            query_embedding = self.embedding_model.encode([query])

            # ChromaDB에서 검색
            results = self.memory_collection.query(
                query_embeddings=query_embedding.tolist(),
                n_results=limit
            )

            return results['documents'][0] if results['documents'] else []

        except Exception as e:
            print(f"메모리 검색 오류: {e}")
            return []

    def save_conversation_to_rag(self, user_input: str, ai_response: str, context: Dict = None):
        """대화를 RAG에 저장"""
        if not self.modules_enabled['rag']:
            return

        try:
            # 대화 텍스트 구성
            conversation_text = f"사용자: {user_input}\n{self.name}: {ai_response}"

            # 임베딩 생성
            embedding = self.embedding_model.encode([conversation_text])

            # ChromaDB에 저장
            doc_id = f"conv_{int(time.time() * 1000)}"
            self.memory_collection.add(
                embeddings=embedding.tolist(),
                documents=[conversation_text],
                ids=[doc_id],
                metadatas=[{
                    "timestamp": datetime.now().isoformat(),
                    "user_input": user_input,
                    "ai_response": ai_response,
                    "context": json.dumps(context) if context else "{}"
                }]
            )

            # SQLite에도 저장
            with self.db_lock:
                cursor = self.db_conn.cursor()
                cursor.execute('''
                    INSERT INTO conversations (user_message, ai_response, context_tags)
                    VALUES (?, ?, ?)
                ''', (user_input, ai_response, json.dumps(context) if context else "{}"))
                self.db_conn.commit()

        except Exception as e:
            print(f"대화 저장 오류: {e}")

    # === 고급 응답 생성 메서드들 ===
    def generate_ollama_response(self, context: str, memories: List[str]) -> str:
        """Ollama API 응답 생성"""
        try:
            # 메모리 컨텍스트 추가
            if memories:
                context += "\n\n관련 기억:\n" + "\n".join(memories[:3])

            response = requests.post(
                self.api_configs['ollama']['stream_url'],
                json={
                    "model": self.api_configs['ollama']['model'],
                    "prompt": context,
                    "stream": False,
                    "options": {
                        "temperature": self.temperature,
                        "top_p": 0.9,
                        "max_tokens": self.max_tokens
                    }
                },
                timeout=30
            )

            if response.status_code == 200:
                result = response.json()
                return result.get("response", "죄송해요, 응답을 생성할 수 없었어요.").strip()
            else:
                return "서버 연결에 문제가 있어요. 잠시 후 다시 시도해주세요."

        except requests.exceptions.ConnectionError:
            return "Ollama 서버에 연결할 수 없어요. Ollama가 실행 중인지 확인해주세요."
        except Exception as e:
            print(f"Ollama API 오류: {e}")
            return "죄송해요, 일시적인 오류가 발생했어요. 다시 시도해주세요."

    def generate_openai_response(self, context: str, memories: List[str]) -> str:
        """OpenAI API 응답 생성"""
        try:
            import openai
            openai.api_key = self.api_configs['openai']['api_key']

            # 메시지 구성
            messages = [
                {"role": "system", "content": self.personality}
            ]

            # 메모리 추가
            if memories:
                memory_context = "관련 기억:\n" + "\n".join(memories[:3])
                messages.append({"role": "system", "content": memory_context})

            # 최근 대화 추가
            for msg in self.chat_history[-10:]:
                messages.append({"role": msg["role"], "content": msg["content"]})

            response = openai.ChatCompletion.create(
                model=self.api_configs['openai']['model'],
                messages=messages,
                max_tokens=self.max_tokens,
                temperature=self.temperature
            )

            return response.choices[0].message.content.strip()

        except Exception as e:
            print(f"OpenAI API 오류: {e}")
            return "OpenAI API 호출 중 오류가 발생했어요."

    def generate_oobabooga_response(self, context: str, memories: List[str]) -> str:
        """Oobabooga API 응답 생성"""
        try:
            # 메모리 컨텍스트 추가
            if memories:
                context += "\n\n관련 기억:\n" + "\n".join(memories[:3])

            # 메시지 형식 구성
            messages = [
                {"role": "system", "content": self.personality},
                {"role": "user", "content": context}
            ]

            response = requests.post(
                self.api_configs['oobabooga']['stream_url'],
                json={
                    "messages": messages,
                    "max_tokens": self.max_tokens,
                    "temperature": self.temperature,
                    "mode": "chat",
                    "stream": False
                },
                timeout=30
            )

            if response.status_code == 200:
                result = response.json()
                return result['choices'][0]['message']['content'].strip()
            else:
                return "Oobabooga 서버 연결에 문제가 있어요."

        except Exception as e:
            print(f"Oobabooga API 오류: {e}")
            return "Oobabooga API 호출 중 오류가 발생했어요."

    # === 고급 음성 처리 ===
    def start_voice_recording(self):
        """음성 녹음 시작"""
        if self.is_listening or self.is_speaking:
            return

        self.is_listening = True
        recording_thread = threading.Thread(target=self.voice_recording_worker, daemon=True)
        recording_thread.start()

    def voice_recording_worker(self):
        """음성 녹음 작업자"""
        try:
            print("🎤 음성 녹음 시작... (말씀하세요)")

            # 음성 녹음
            duration = 5  # 최대 5초
            audio_data = sd.rec(int(duration * self.sample_rate),
                              samplerate=self.sample_rate,
                              channels=1, dtype='float32')
            sd.wait()

            # 임시 파일로 저장
            temp_file = "temp_audio.wav"
            sf.write(temp_file, audio_data, self.sample_rate)

            # 음성 인식
            transcribed_text = self.transcribe_audio_advanced(temp_file)

            if transcribed_text.strip():
                print(f"🎤 인식된 텍스트: {transcribed_text}")

                # AI 응답 생성 및 처리
                response = self.generate_response_advanced(transcribed_text)

                # 대화 히스토리 업데이트
                self.chat_history.append({"role": "user", "content": transcribed_text})
                self.chat_history.append({"role": "assistant", "content": response})

                # RAG에 저장
                self.save_conversation_to_rag(transcribed_text, response)

                # 음성으로 응답
                self.speak_advanced(response)

                # VTube Studio 연동
                if self.modules_enabled['vtube'] and hasattr(self, 'vtube_integration'):
                    self.vtube_integration.process_ai_response(response)

            # 임시 파일 삭제
            try:
                os.remove(temp_file)
            except:
                pass

        except Exception as e:
            print(f"음성 녹음 오류: {e}")
        finally:
            self.is_listening = False

    def transcribe_audio_advanced(self, audio_file: str) -> str:
        """고급 음성 인식"""
        try:
            # VAD로 음성 구간 감지
            wav = read_audio(audio_file, sampling_rate=self.sample_rate)
            speech_timestamps = get_speech_timestamps(wav, self.vad_model,
                                                    sampling_rate=self.sample_rate)

            if not speech_timestamps:
                return ""

            # Faster Whisper로 인식
            segments, info = self.whisper_model.transcribe(
                audio_file,
                beam_size=5,
                language="ko",
                temperature=0.0
            )

            result = ""
            for segment in segments:
                result += segment.text + " "

            return result.strip()

        except Exception as e:
            print(f"음성 인식 오류: {e}")
            return ""

    def speak_advanced(self, text: str):
        """고급 음성 합성"""
        if not self.modules_enabled['tts']:
            return

        # TTS 큐에 추가
        self.tts_queue.put(text)

    # === 유틸리티 메서드들 ===
    def regenerate_last_response(self):
        """마지막 응답 재생성"""
        if len(self.chat_history) < 2:
            return

        # 마지막 사용자 메시지 찾기
        last_user_message = None
        for msg in reversed(self.chat_history):
            if msg["role"] == "user":
                last_user_message = msg["content"]
                break

        if last_user_message:
            # 새 응답 생성
            new_response = self.generate_response_advanced(last_user_message)

            # 히스토리 업데이트 (마지막 AI 응답 교체)
            for i in reversed(range(len(self.chat_history))):
                if self.chat_history[i]["role"] == "assistant":
                    self.chat_history[i]["content"] = new_response
                    break

            # 음성으로 응답
            self.speak_advanced(new_response)

            print(f"🔄 재생성된 응답: {new_response}")

    def toggle_listening_mode(self):
        """듣기 모드 토글"""
        if self.is_listening:
            self.is_listening = False
            print("🔇 듣기 모드 비활성화")
        else:
            print("🔊 듣기 모드 활성화")
            self.start_voice_recording()

    def emergency_stop(self):
        """긴급 정지"""
        self.is_listening = False
        self.is_speaking = False

        # TTS 큐 비우기
        while not self.tts_queue.empty():
            try:
                self.tts_queue.get_nowait()
            except:
                break

        print("🛑 긴급 정지 실행됨")

    def switch_mode(self):
        """모드 전환"""
        modes = ["normal", "gaming", "hangout", "creative"]
        current_index = modes.index(self.current_mode)
        next_index = (current_index + 1) % len(modes)
        self.current_mode = modes[next_index]

        print(f"🔄 모드 변경: {self.current_mode}")

    # === 선택적 모듈 설정 ===
    def setup_discord_bot(self):
        """Discord 봇 설정"""
        try:
            from modules.discord_bot import AdvancedDiscordBot
            self.discord_bot = AdvancedDiscordBot(self)

            # 별도 스레드에서 실행
            discord_thread = threading.Thread(target=self.discord_bot.run_bot, daemon=True)
            discord_thread.start()

            print("  ✅ Discord 봇 시작됨")
        except Exception as e:
            print(f"  ❌ Discord 봇 설정 실패: {e}")
            self.modules_enabled['discord'] = False

    def setup_vtube_studio(self):
        """VTube Studio 설정"""
        try:
            from modules.vtube_studio import VTubeStudioIntegration
            self.vtube_integration = VTubeStudioIntegration(self)
            self.vtube_integration.start_vtube_integration()

            print("  ✅ VTube Studio 연동 시작됨")
        except Exception as e:
            print(f"  ❌ VTube Studio 설정 실패: {e}")
            self.modules_enabled['vtube'] = False

    def setup_vision_system(self):
        """컴퓨터 비전 시스템 설정"""
        try:
            # OpenCV 초기화
            self.camera = cv2.VideoCapture(0)
            print("  ✅ 컴퓨터 비전 시스템 준비 완료")
        except Exception as e:
            print(f"  ❌ 컴퓨터 비전 설정 실패: {e}")
            self.modules_enabled['vision'] = False

    def setup_gaming_system(self):
        """게임 연동 시스템 설정"""
        try:
            # 게임 연동 초기화
            print("  ✅ 게임 연동 시스템 준비 완료")
        except Exception as e:
            print(f"  ❌ 게임 연동 설정 실패: {e}")
            self.modules_enabled['gaming'] = False

    def setup_alarm_system(self):
        """알람 시스템 설정"""
        try:
            # 알람 시스템 초기화
            print("  ✅ 알람 시스템 준비 완료")
        except Exception as e:
            print(f"  ❌ 알람 시스템 설정 실패: {e}")
            self.modules_enabled['alarm'] = False

    # === 고급 웹 UI ===
    def create_advanced_interface(self):
        """고급 웹 인터페이스 생성"""
        # 커스텀 CSS
        custom_css = """
        .gradio-container {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .chat-message {
            border-radius: 15px;
            padding: 15px;
            margin: 10px 0;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .status-panel {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 15px;
            backdrop-filter: blur(10px);
        }
        .module-status {
            display: inline-block;
            padding: 5px 10px;
            border-radius: 20px;
            margin: 2px;
            font-size: 12px;
        }
        .module-active {
            background: #4CAF50;
            color: white;
        }
        .module-inactive {
            background: #f44336;
            color: white;
        }
        """

        # 테마 선택
        theme_map = {
            "soft": gr.themes.Soft(),
            "glass": gr.themes.Glass(),
            "monochrome": gr.themes.Monochrome()
        }
        theme = theme_map.get(self.ui_config['theme'], gr.themes.Soft())

        with gr.Blocks(
            title=f"{self.name} - 고성능 AI 어시스턴트",
            theme=theme,
            css=custom_css
        ) as interface:

            # 헤더
            gr.Markdown(f"""
            # 🚀 {self.name} - 고성능 AI 어시스턴트

            **Z-Waif 수준의 기능과 성능을 갖춘 차세대 AI 어시스턴트**

            안녕하세요, {self.user_name}님! 저는 **{self.name}**이에요.
            음성, 텍스트, Discord 등 다양한 방법으로 대화할 수 있어요! ✨
            """)

            # 상태 패널
            with gr.Row():
                with gr.Column(scale=1):
                    status_display = gr.HTML(
                        value=self.get_status_html(),
                        label="시스템 상태"
                    )

                    # 실시간 업데이트 버튼
                    refresh_btn = gr.Button("🔄 상태 새로고침", variant="secondary")

            with gr.Row():
                # 메인 채팅 영역
                with gr.Column(scale=3):
                    chatbot = gr.Chatbot(
                        label=f"{self.name}와의 대화",
                        height=600,
                        avatar_images=("👤", "🤖"),
                        bubble_full_width=False,
                        show_copy_button=True
                    )

                    # 입력 영역
                    with gr.Row():
                        msg = gr.Textbox(
                            label="메시지 입력",
                            placeholder=f"{self.name}에게 무엇이든 물어보세요...",
                            scale=4,
                            lines=2,
                            max_lines=5
                        )
                        send_btn = gr.Button("전송 📤", scale=1, variant="primary")

                    # 음성 및 제어 버튼
                    with gr.Row():
                        audio_input = gr.Audio(
                            label="🎤 음성 입력",
                            type="filepath",
                            scale=2
                        )

                        with gr.Column(scale=1):
                            voice_btn = gr.Button("🎤 음성 녹음", variant="secondary")
                            regen_btn = gr.Button("🔄 재생성", variant="secondary")
                            clear_btn = gr.Button("🗑️ 대화 초기화", variant="secondary")

                # 제어 패널
                with gr.Column(scale=1):
                    gr.Markdown("### 🎛️ 제어 패널")

                    # 모드 선택
                    with gr.Accordion("🎯 AI 모드", open=True):
                        mode_radio = gr.Radio(
                            choices=["normal", "gaming", "hangout", "creative"],
                            value=self.current_mode,
                            label="현재 모드"
                        )
                        mode_btn = gr.Button("모드 변경", variant="secondary")

                    # 모듈 제어
                    with gr.Accordion("🔧 모듈 제어", open=False):
                        module_checkboxes = {}
                        for module, enabled in self.modules_enabled.items():
                            module_checkboxes[module] = gr.Checkbox(
                                label=f"{module.upper()}",
                                value=enabled
                            )

                    # 고급 설정
                    with gr.Accordion("⚙️ 고급 설정", open=False):
                        temperature_slider = gr.Slider(
                            minimum=0.1,
                            maximum=2.0,
                            value=self.temperature,
                            step=0.1,
                            label="창의성 (Temperature)"
                        )

                        max_tokens_slider = gr.Slider(
                            minimum=50,
                            maximum=1000,
                            value=self.max_tokens,
                            step=50,
                            label="최대 토큰 수"
                        )

                        api_select = gr.Dropdown(
                            choices=["ollama", "openai", "oobabooga"],
                            value=self.primary_api,
                            label="API 선택"
                        )

                    # 메모리 관리
                    with gr.Accordion("🧠 메모리 관리", open=False):
                        memory_search = gr.Textbox(
                            label="메모리 검색",
                            placeholder="검색할 내용을 입력하세요..."
                        )
                        memory_btn = gr.Button("🔍 검색", variant="secondary")
                        memory_results = gr.Textbox(
                            label="검색 결과",
                            lines=5,
                            interactive=False
                        )

                        clear_memory_btn = gr.Button("🗑️ 메모리 초기화", variant="stop")

                    # 대화 관리
                    with gr.Accordion("💾 대화 관리", open=False):
                        save_btn = gr.Button("💾 대화 저장", variant="secondary")
                        load_btn = gr.Button("📂 대화 불러오기", variant="secondary")
                        export_btn = gr.Button("📤 대화 내보내기", variant="secondary")

                        save_status = gr.Textbox(
                            label="저장 상태",
                            interactive=False,
                            placeholder="저장 결과가 여기에 표시됩니다"
                        )

            # 통계 및 정보
            with gr.Row():
                with gr.Accordion("📊 통계 정보", open=False):
                    stats_display = gr.HTML(value=self.get_stats_html())

            # === 이벤트 핸들러 ===

            # 기본 채팅
            msg.submit(self.chat_interface, [msg, chatbot], [chatbot, msg])
            send_btn.click(self.chat_interface, [msg, chatbot], [chatbot, msg])

            # 음성 채팅
            audio_input.change(self.voice_chat_interface, [audio_input, chatbot], [chatbot, msg])
            voice_btn.click(self.start_voice_recording_interface, None, None)

            # 제어 버튼
            regen_btn.click(self.regenerate_interface, chatbot, chatbot)
            clear_btn.click(lambda: ([], []), None, [chatbot, msg])

            # 모드 변경
            mode_btn.click(self.change_mode_interface, mode_radio, None)

            # 설정 변경
            temperature_slider.change(self.update_temperature, temperature_slider, None)
            max_tokens_slider.change(self.update_max_tokens, max_tokens_slider, None)
            api_select.change(self.update_api, api_select, None)

            # 메모리 관리
            memory_btn.click(self.search_memory_interface, memory_search, memory_results)
            clear_memory_btn.click(self.clear_memory_interface, None, memory_results)

            # 대화 관리
            save_btn.click(self.save_conversation_interface, chatbot, save_status)

            # 상태 새로고침
            refresh_btn.click(self.refresh_status, None, status_display)

            # 자동 새로고침 (10초마다)
            interface.load(self.auto_refresh_status, None, status_display, every=10)

        return interface

    # === 인터페이스 메서드들 ===
    def chat_interface(self, message: str, history: List[Tuple[str, str]]) -> Tuple[List[Tuple[str, str]], str]:
        """채팅 인터페이스 처리"""
        if not message.strip():
            return history, ""

        print(f"💬 사용자: {message}")

        # AI 응답 생성
        response = self.generate_response_advanced(message)
        print(f"🤖 {self.name}: {response}")

        # 히스토리 업데이트
        history.append((message, response))

        # 내부 히스토리 업데이트
        self.chat_history.append({"role": "user", "content": message})
        self.chat_history.append({"role": "assistant", "content": response})

        # RAG에 저장
        self.save_conversation_to_rag(message, response)

        # 음성으로 응답
        self.speak_advanced(response)

        # VTube Studio 연동
        if self.modules_enabled['vtube'] and hasattr(self, 'vtube_integration'):
            self.vtube_integration.process_ai_response(response)

        return history, ""

    def voice_chat_interface(self, audio_file, history: List[Tuple[str, str]]) -> Tuple[List[Tuple[str, str]], str]:
        """음성 채팅 인터페이스 처리"""
        if audio_file is None:
            return history, ""

        # 음성을 텍스트로 변환
        user_message = self.transcribe_audio_advanced(audio_file)

        if user_message:
            return self.chat_interface(user_message, history)
        else:
            return history, ""

    def start_voice_recording_interface(self):
        """음성 녹음 시작 인터페이스"""
        self.start_voice_recording()
        return "🎤 음성 녹음 시작됨"

    def regenerate_interface(self, history: List[Tuple[str, str]]) -> List[Tuple[str, str]]:
        """재생성 인터페이스"""
        if not history:
            return history

        # 마지막 사용자 메시지 가져오기
        last_user_message = history[-1][0] if history else ""

        if last_user_message:
            # 새 응답 생성
            new_response = self.generate_response_advanced(last_user_message)

            # 히스토리 업데이트
            history[-1] = (last_user_message, new_response)

            # 내부 히스토리도 업데이트
            if self.chat_history and self.chat_history[-1]["role"] == "assistant":
                self.chat_history[-1]["content"] = new_response

            # 음성으로 응답
            self.speak_advanced(new_response)

            print(f"🔄 재생성: {new_response}")

        return history

    def change_mode_interface(self, mode: str):
        """모드 변경 인터페이스"""
        self.current_mode = mode
        print(f"🎯 모드 변경: {mode}")
        return f"모드가 {mode}로 변경되었습니다."

    def update_temperature(self, temperature: float):
        """온도 설정 업데이트"""
        self.temperature = temperature
        print(f"🌡️ 창의성 설정: {temperature}")

    def update_max_tokens(self, max_tokens: int):
        """최대 토큰 수 업데이트"""
        self.max_tokens = max_tokens
        print(f"📝 최대 토큰: {max_tokens}")

    def update_api(self, api_type: str):
        """API 타입 업데이트"""
        self.primary_api = api_type
        print(f"🔗 API 변경: {api_type}")

    def search_memory_interface(self, query: str) -> str:
        """메모리 검색 인터페이스"""
        if not query.strip():
            return "검색어를 입력해주세요."

        memories = self.search_memories(query, limit=5)

        if memories:
            result = f"'{query}' 관련 기억 {len(memories)}개:\n\n"
            for i, memory in enumerate(memories, 1):
                result += f"{i}. {memory[:200]}...\n\n"
            return result
        else:
            return f"'{query}'와 관련된 기억을 찾을 수 없습니다."

    def clear_memory_interface(self) -> str:
        """메모리 초기화 인터페이스"""
        try:
            if self.modules_enabled['rag']:
                # ChromaDB 컬렉션 삭제 후 재생성
                self.chroma_client.delete_collection("conversation_memory")
                self.memory_collection = self.chroma_client.get_or_create_collection(
                    name="conversation_memory",
                    metadata={"description": "Long-term conversation memory"}
                )

                # SQLite 테이블 초기화
                with self.db_lock:
                    cursor = self.db_conn.cursor()
                    cursor.execute("DELETE FROM conversations")
                    self.db_conn.commit()

                return "✅ 메모리가 초기화되었습니다."
            else:
                return "❌ RAG 시스템이 비활성화되어 있습니다."
        except Exception as e:
            return f"❌ 메모리 초기화 실패: {str(e)}"

    def save_conversation_interface(self, history: List[Tuple[str, str]]) -> str:
        """대화 저장 인터페이스"""
        if not history:
            return "저장할 대화가 없습니다."

        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"conversations/conversation_{timestamp}.json"

            os.makedirs("conversations", exist_ok=True)

            # 대화 데이터 구성
            conversation_data = {
                "timestamp": datetime.now().isoformat(),
                "ai_name": self.name,
                "user_name": self.user_name,
                "mode": self.current_mode,
                "conversation": history,
                "total_messages": len(history)
            }

            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(conversation_data, f, ensure_ascii=False, indent=2)

            return f"✅ 대화가 {filename}에 저장되었습니다."

        except Exception as e:
            return f"❌ 저장 중 오류: {str(e)}"

    def get_status_html(self) -> str:
        """상태 HTML 생성"""
        status_html = f"""
        <div class="status-panel">
            <h3>🤖 {self.name} 상태</h3>
            <p><strong>모드:</strong> {self.current_mode}</p>
            <p><strong>상태:</strong> {'🟢 준비됨' if self.system_ready else '🔴 초기화 중'}</p>
            <p><strong>음성:</strong> {'🔊 활성' if not self.is_speaking else '🎤 말하는 중'}</p>
            <p><strong>듣기:</strong> {'👂 활성' if self.is_listening else '🔇 대기 중'}</p>

            <h4>활성 모듈:</h4>
            <div>
        """

        for module, enabled in self.modules_enabled.items():
            status_class = "module-active" if enabled else "module-inactive"
            status_html += f'<span class="{status_class}">{module.upper()}</span>'

        status_html += """
            </div>
        </div>
        """

        return status_html

    def get_stats_html(self) -> str:
        """통계 HTML 생성"""
        total_conversations = len(self.chat_history) // 2
        memory_count = 0

        if self.modules_enabled['rag']:
            try:
                memory_count = self.memory_collection.count()
            except:
                memory_count = 0

        stats_html = f"""
        <div style="display: flex; justify-content: space-around; text-align: center;">
            <div>
                <h4>💬 총 대화</h4>
                <p style="font-size: 24px; font-weight: bold;">{total_conversations}</p>
            </div>
            <div>
                <h4>🧠 저장된 기억</h4>
                <p style="font-size: 24px; font-weight: bold;">{memory_count}</p>
            </div>
            <div>
                <h4>🎯 현재 모드</h4>
                <p style="font-size: 18px; font-weight: bold;">{self.current_mode.upper()}</p>
            </div>
            <div>
                <h4>🔗 API</h4>
                <p style="font-size: 18px; font-weight: bold;">{self.primary_api.upper()}</p>
            </div>
        </div>
        """

        return stats_html

    def refresh_status(self) -> str:
        """상태 새로고침"""
        return self.get_status_html()

    def auto_refresh_status(self) -> str:
        """자동 상태 새로고침"""
        return self.get_status_html()

def main():
    """메인 실행 함수"""
    print("=" * 80)
    print("🚀 고성능 AI 어시스턴트 시스템 v2.0")
    print("Z-Waif 수준의 기능과 성능을 갖춘 차세대 AI 어시스턴트")
    print("=" * 80)

    try:
        # AI 어시스턴트 인스턴스 생성
        print("🔧 시스템 초기화 중...")
        assistant = AdvancedAIAssistant()

        # 시스템 준비 대기
        while not assistant.system_ready:
            time.sleep(0.1)

        # 인터페이스 생성
        print("🌐 웹 인터페이스 생성 중...")
        interface = assistant.create_advanced_interface()

        # 시작 메시지
        print("=" * 80)
        print(f"✨ {assistant.name} 고성능 AI 어시스턴트가 준비되었습니다!")
        print(f"👤 사용자: {assistant.user_name}")
        print(f"🎯 모드: {assistant.current_mode}")
        print(f"🔗 API: {assistant.primary_api}")
        print("=" * 80)
        print("🌐 웹 브라우저가 자동으로 열립니다...")
        print(f"🔗 수동 접속: http://localhost:{assistant.ui_config['port']}")
        print("⌨️  핫키 사용 가능 (스페이스바: 음성 입력)")
        print("🛑 종료: Ctrl+C")
        print("=" * 80)

        # 환경 정보 출력
        active_modules = [k.upper() for k, v in assistant.modules_enabled.items() if v]
        print(f"📦 활성 모듈: {', '.join(active_modules)}")

        if assistant.modules_enabled['discord']:
            print("💬 Discord 봇이 활성화되었습니다.")
        if assistant.modules_enabled['vtube']:
            print("🎭 VTube Studio 연동이 활성화되었습니다.")
        if assistant.modules_enabled['rag']:
            print("🧠 RAG 장기 기억 시스템이 활성화되었습니다.")

        print("=" * 80)

        # 인터페이스 실행
        interface.launch(
            server_name=assistant.ui_config['host'],
            server_port=assistant.ui_config['port'],
            share=assistant.ui_config['share'],
            inbrowser=True,
            show_error=True,
            quiet=False
        )

    except KeyboardInterrupt:
        print("\n🛑 사용자에 의해 종료되었습니다.")
    except Exception as e:
        print(f"❌ 시스템 시작 오류: {e}")
        print("📋 다음을 확인해주세요:")
        print("  1. Python 3.8 이상 설치")
        print("  2. requirements.txt의 모든 패키지 설치")
        print("  3. Ollama 서버 실행 (ollama serve)")
        print("  4. 필요한 모델 다운로드 (ollama pull llama2)")
        print("  5. 환경 변수 설정 (.env 파일)")
    finally:
        print("👋 고성능 AI 어시스턴트를 이용해주셔서 감사합니다!")

if __name__ == "__main__":
    main()
    
    def generate_response_ollama(self, user_input: str) -> str:
        """Ollama를 사용한 응답 생성"""
        context = self.build_context(user_input)
        
        try:
            response = requests.post(
                self.api_url,
                json={
                    "model": self.model_name,
                    "prompt": context,
                    "stream": False,
                    "options": {
                        "temperature": 0.7,
                        "top_p": 0.9,
                        "max_tokens": 500
                    }
                },
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                return result.get("response", "죄송해요, 응답을 생성할 수 없었어요.").strip()
            else:
                return "서버 연결에 문제가 있어요. 잠시 후 다시 시도해주세요."
                
        except requests.exceptions.ConnectionError:
            return "Ollama 서버에 연결할 수 없어요. Ollama가 실행 중인지 확인해주세요."
        except Exception as e:
            print(f"Ollama API 오류: {e}")
            return "죄송해요, 일시적인 오류가 발생했어요. 다시 시도해주세요."
    
    def generate_response_openai(self, user_input: str) -> str:
        """OpenAI API를 사용한 응답 생성"""
        try:
            import openai
            openai.api_key = self.openai_api_key
            
            messages = [
                {"role": "system", "content": self.personality},
                *[{"role": msg["role"], "content": msg["content"]} 
                  for msg in self.chat_history[-10:]],  # 최근 10개 대화
                {"role": "user", "content": user_input}
            ]
            
            response = openai.ChatCompletion.create(
                model="gpt-3.5-turbo",
                messages=messages,
                max_tokens=500,
                temperature=0.7
            )
            
            return response.choices[0].message.content.strip()
            
        except Exception as e:
            print(f"OpenAI API 오류: {e}")
            return "OpenAI API 호출 중 오류가 발생했어요."
    
    def build_context(self, user_input: str) -> str:
        """대화 컨텍스트 구성"""
        context = self.personality + "\n\n"
        context += "이전 대화:\n"
        
        # 최근 5개 대화만 포함
        for msg in self.chat_history[-5:]:
            role = "사용자" if msg["role"] == "user" else "Luna"
            context += f"{role}: {msg['content']}\n"
        
        context += f"\n사용자: {user_input}\nLuna: "
        return context
    
    def generate_response(self, user_input: str) -> str:
        """AI 응답 생성 (메인 함수)"""
        if self.use_openai:
            return self.generate_response_openai(user_input)
        else:
            return self.generate_response_ollama(user_input)
    
    def speak(self, text: str):
        """텍스트를 음성으로 변환"""
        if self.is_speaking:
            return
        
        def _speak():
            self.is_speaking = True
            try:
                # 특수 문자 제거
                clean_text = text.replace("*", "").replace("_", "").replace("#", "")
                self.tts_engine.say(clean_text)
                self.tts_engine.runAndWait()
            except Exception as e:
                print(f"음성 합성 오류: {e}")
            finally:
                self.is_speaking = False
        
        thread = threading.Thread(target=_speak)
        thread.daemon = True
        thread.start()
    
    def process_audio(self, audio_file) -> str:
        """음성 파일을 텍스트로 변환"""
        if audio_file is None:
            return ""
        
        try:
            print("🎤 음성 인식 중...")
            result = self.whisper_model.transcribe(audio_file, language="ko")
            transcribed_text = result["text"].strip()
            print(f"인식된 텍스트: {transcribed_text}")
            return transcribed_text
        except Exception as e:
            print(f"음성 인식 오류: {e}")
            return ""
    
    def chat(self, message: str, history: List[Tuple[str, str]]) -> Tuple[List[Tuple[str, str]], str]:
        """채팅 처리"""
        if not message.strip():
            return history, ""
        
        print(f"사용자 입력: {message}")
        
        # AI 응답 생성
        response = self.generate_response(message)
        print(f"AI 응답: {response}")
        
        # 히스토리 업데이트
        history.append((message, response))
        
        # 내부 히스토리 업데이트
        self.chat_history.append({"role": "user", "content": message})
        self.chat_history.append({"role": "assistant", "content": response})
        
        # 음성으로 응답
        self.speak(response)
        
        return history, ""
    
    def voice_chat(self, audio_file, history: List[Tuple[str, str]]) -> Tuple[List[Tuple[str, str]], str]:
        """음성 채팅 처리"""
        if audio_file is None:
            return history, ""
        
        # 음성을 텍스트로 변환
        user_message = self.process_audio(audio_file)
        
        if user_message:
            return self.chat(user_message, history)
        else:
            return history, ""
    
    def save_conversation(self, history: List[Tuple[str, str]]):
        """대화 저장"""
        if not history:
            return "저장할 대화가 없습니다."
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"conversations/conversation_{timestamp}.json"
        
        os.makedirs("conversations", exist_ok=True)
        
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(history, f, ensure_ascii=False, indent=2)
            return f"대화가 {filename}에 저장되었습니다."
        except Exception as e:
            return f"저장 중 오류 발생: {e}"
    
    def create_interface(self):
        """Gradio 인터페이스 생성"""
        # 커스텀 CSS
        custom_css = """
        .gradio-container {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .chat-message {
            border-radius: 10px;
            padding: 10px;
            margin: 5px 0;
        }
        """
        
        with gr.Blocks(
            title=f"{self.name} - AI 어시스턴트", 
            theme=gr.themes.Soft(),
            css=custom_css
        ) as interface:
            
            gr.Markdown(f"""
            # 🤖 {self.name} - 나만의 AI 어시스턴트
            
            안녕하세요! 저는 **{self.name}**이에요. 무엇이든 물어보세요! 
            텍스트로 채팅하거나 음성으로 대화할 수 있어요. 🎤✨
            """)
            
            with gr.Row():
                with gr.Column(scale=3):
                    # 채팅 인터페이스
                    chatbot = gr.Chatbot(
                        label=f"{self.name}와의 대화",
                        height=500,
                        avatar_images=("👤", "🤖"),
                        bubble_full_width=False
                    )
                    
                    # 텍스트 입력
                    with gr.Row():
                        msg = gr.Textbox(
                            label="메시지 입력",
                            placeholder=f"{self.name}에게 무엇이든 물어보세요...",
                            scale=4,
                            lines=2
                        )
                        send_btn = gr.Button("전송 📤", scale=1, variant="primary")
                    
                    # 음성 입력
                    with gr.Row():
                        audio_input = gr.Audio(
                            label="🎤 음성 입력 (녹음 후 자동 처리)",
                            type="filepath",
                            scale=3
                        )
                        clear_btn = gr.Button("대화 초기화 🗑️", scale=1)
                
                with gr.Column(scale=1):
                    gr.Markdown("### ⚙️ 설정 및 정보")
                    
                    with gr.Accordion("🤖 AI 정보", open=True):
                        gr.Markdown(f"""
                        **이름**: {self.name}  
                        **타입**: 친근한 AI 어시스턴트  
                        **기능**: 
                        - 💬 자연어 대화
                        - 🎤 음성 인식  
                        - 🔊 음성 합성
                        - 🧠 컨텍스트 기억
                        """)
                    
                    with gr.Accordion("📊 상태", open=False):
                        status_text = gr.Textbox(
                            label="시스템 상태",
                            value="✅ 정상 작동 중",
                            interactive=False
                        )
                    
                    with gr.Accordion("💾 대화 관리", open=False):
                        save_btn = gr.Button("대화 저장 💾", variant="secondary")
                        save_status = gr.Textbox(
                            label="저장 상태",
                            interactive=False,
                            placeholder="저장 결과가 여기에 표시됩니다"
                        )
                    
                    with gr.Accordion("ℹ️ 사용법", open=False):
                        gr.Markdown("""
                        **텍스트 채팅**:
                        1. 아래 입력창에 메시지 입력
                        2. 전송 버튼 클릭 또는 Enter
                        
                        **음성 채팅**:
                        1. 마이크 버튼 클릭하여 녹음
                        2. 녹음 완료 후 자동 처리
                        
                        **기타**:
                        - 대화 초기화: 모든 대화 삭제
                        - 대화 저장: JSON 파일로 저장
                        """)
            
            # 이벤트 핸들러
            msg.submit(self.chat, [msg, chatbot], [chatbot, msg])
            send_btn.click(self.chat, [msg, chatbot], [chatbot, msg])
            audio_input.change(self.voice_chat, [audio_input, chatbot], [chatbot, msg])
            clear_btn.click(lambda: ([], []), None, [chatbot, msg])
            save_btn.click(self.save_conversation, chatbot, save_status)
        
        return interface

def main():
    """메인 실행 함수"""
    print("=" * 50)
    print("🚀 나만의 AI 어시스턴트 시스템 시작")
    print("=" * 50)
    
    try:
        # AI 어시스턴트 인스턴스 생성
        assistant = MyAIAssistant()
        
        # 인터페이스 생성 및 실행
        interface = assistant.create_interface()
        
        print(f"✨ {assistant.name} AI 어시스턴트가 준비되었습니다!")
        print("🌐 웹 브라우저가 자동으로 열립니다...")
        print("📝 Ollama가 실행 중이어야 AI 응답이 가능합니다.")
        print("🔗 수동 접속: http://localhost:7860")
        print("=" * 50)
        
        interface.launch(
            server_name="0.0.0.0",
            server_port=7860,
            share=False,
            inbrowser=True,
            show_error=True
        )
        
    except Exception as e:
        print(f"❌ 시스템 시작 오류: {e}")
        print("📋 requirements.txt의 모든 패키지가 설치되었는지 확인해주세요.")

if __name__ == "__main__":
    main()
