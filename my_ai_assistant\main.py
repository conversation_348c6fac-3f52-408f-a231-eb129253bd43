#!/usr/bin/env python3
"""
나만의 AI 어시스턴트 시스템 v1.0
기본 채팅 + 음성 기능
"""

import gradio as gr
import whisper
import pyttsx3
import threading
import json
import os
import requests
from datetime import datetime
from typing import List, Tuple, Dict

class MyAIAssistant:
    def __init__(self):
        self.name = "<PERSON>"  # AI 이름
        self.user_name = "사용자"  # 사용자 호칭
        self.personality = self.load_personality()
        self.chat_history = []
        self.is_speaking = False
        
        # 음성 설정
        print("🎤 음성 인식 모델 로딩 중...")
        self.whisper_model = whisper.load_model("base")
        
        print("🔊 음성 합성 엔진 초기화 중...")
        self.tts_engine = pyttsx3.init()
        self.setup_voice()
        
        # LLM 설정
        self.setup_llm()
        
        print("✨ AI 어시스턴트 준비 완료!")
    
    def load_personality(self) -> str:
        """캐릭터 성격 설정"""
        return """
        당신은 Luna라는 이름의 친근한 AI 어시스턴트입니다.
        
        성격과 특징:
        - 매우 친근하고 도움이 되고 싶어함
        - 약간의 유머 감각이 있음
        - 정중하지만 친구같은 말투
        - 사용자의 질문에 성실하게 답변
        - 모르는 것은 솔직히 모른다고 말함
        
        말투 예시:
        - "안녕하세요! 무엇을 도와드릴까요?"
        - "흥미로운 질문이네요! 제가 알아본 바로는..."
        - "죄송해요, 그 부분은 잘 모르겠어요. 다른 방법으로 도와드릴까요?"
        
        항상 도움이 되는 정보를 제공하려고 노력하세요.
        """
    
    def setup_voice(self):
        """TTS 음성 설정"""
        try:
            voices = self.tts_engine.getProperty('voices')
            if voices:
                # 한국어 또는 여성 음성 우선 선택
                for voice in voices:
                    if any(keyword in voice.name.lower() for keyword in ['korean', 'female', 'woman', 'zira']):
                        self.tts_engine.setProperty('voice', voice.id)
                        break
            
            self.tts_engine.setProperty('rate', 200)  # 말하기 속도
            self.tts_engine.setProperty('volume', 0.9)  # 볼륨
        except Exception as e:
            print(f"음성 설정 오류: {e}")
    
    def setup_llm(self):
        """LLM 설정"""
        # Ollama 기본 설정
        self.api_url = "http://localhost:11434/api/generate"
        self.model_name = "llama2"
        
        # OpenAI API 설정 (선택사항)
        self.openai_api_key = os.getenv("OPENAI_API_KEY")
        self.use_openai = bool(self.openai_api_key)
    
    def generate_response_ollama(self, user_input: str) -> str:
        """Ollama를 사용한 응답 생성"""
        context = self.build_context(user_input)
        
        try:
            response = requests.post(
                self.api_url,
                json={
                    "model": self.model_name,
                    "prompt": context,
                    "stream": False,
                    "options": {
                        "temperature": 0.7,
                        "top_p": 0.9,
                        "max_tokens": 500
                    }
                },
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                return result.get("response", "죄송해요, 응답을 생성할 수 없었어요.").strip()
            else:
                return "서버 연결에 문제가 있어요. 잠시 후 다시 시도해주세요."
                
        except requests.exceptions.ConnectionError:
            return "Ollama 서버에 연결할 수 없어요. Ollama가 실행 중인지 확인해주세요."
        except Exception as e:
            print(f"Ollama API 오류: {e}")
            return "죄송해요, 일시적인 오류가 발생했어요. 다시 시도해주세요."
    
    def generate_response_openai(self, user_input: str) -> str:
        """OpenAI API를 사용한 응답 생성"""
        try:
            import openai
            openai.api_key = self.openai_api_key
            
            messages = [
                {"role": "system", "content": self.personality},
                *[{"role": msg["role"], "content": msg["content"]} 
                  for msg in self.chat_history[-10:]],  # 최근 10개 대화
                {"role": "user", "content": user_input}
            ]
            
            response = openai.ChatCompletion.create(
                model="gpt-3.5-turbo",
                messages=messages,
                max_tokens=500,
                temperature=0.7
            )
            
            return response.choices[0].message.content.strip()
            
        except Exception as e:
            print(f"OpenAI API 오류: {e}")
            return "OpenAI API 호출 중 오류가 발생했어요."
    
    def build_context(self, user_input: str) -> str:
        """대화 컨텍스트 구성"""
        context = self.personality + "\n\n"
        context += "이전 대화:\n"
        
        # 최근 5개 대화만 포함
        for msg in self.chat_history[-5:]:
            role = "사용자" if msg["role"] == "user" else "Luna"
            context += f"{role}: {msg['content']}\n"
        
        context += f"\n사용자: {user_input}\nLuna: "
        return context
    
    def generate_response(self, user_input: str) -> str:
        """AI 응답 생성 (메인 함수)"""
        if self.use_openai:
            return self.generate_response_openai(user_input)
        else:
            return self.generate_response_ollama(user_input)
    
    def speak(self, text: str):
        """텍스트를 음성으로 변환"""
        if self.is_speaking:
            return
        
        def _speak():
            self.is_speaking = True
            try:
                # 특수 문자 제거
                clean_text = text.replace("*", "").replace("_", "").replace("#", "")
                self.tts_engine.say(clean_text)
                self.tts_engine.runAndWait()
            except Exception as e:
                print(f"음성 합성 오류: {e}")
            finally:
                self.is_speaking = False
        
        thread = threading.Thread(target=_speak)
        thread.daemon = True
        thread.start()
    
    def process_audio(self, audio_file) -> str:
        """음성 파일을 텍스트로 변환"""
        if audio_file is None:
            return ""
        
        try:
            print("🎤 음성 인식 중...")
            result = self.whisper_model.transcribe(audio_file, language="ko")
            transcribed_text = result["text"].strip()
            print(f"인식된 텍스트: {transcribed_text}")
            return transcribed_text
        except Exception as e:
            print(f"음성 인식 오류: {e}")
            return ""
    
    def chat(self, message: str, history: List[Tuple[str, str]]) -> Tuple[List[Tuple[str, str]], str]:
        """채팅 처리"""
        if not message.strip():
            return history, ""
        
        print(f"사용자 입력: {message}")
        
        # AI 응답 생성
        response = self.generate_response(message)
        print(f"AI 응답: {response}")
        
        # 히스토리 업데이트
        history.append((message, response))
        
        # 내부 히스토리 업데이트
        self.chat_history.append({"role": "user", "content": message})
        self.chat_history.append({"role": "assistant", "content": response})
        
        # 음성으로 응답
        self.speak(response)
        
        return history, ""
    
    def voice_chat(self, audio_file, history: List[Tuple[str, str]]) -> Tuple[List[Tuple[str, str]], str]:
        """음성 채팅 처리"""
        if audio_file is None:
            return history, ""
        
        # 음성을 텍스트로 변환
        user_message = self.process_audio(audio_file)
        
        if user_message:
            return self.chat(user_message, history)
        else:
            return history, ""
    
    def save_conversation(self, history: List[Tuple[str, str]]):
        """대화 저장"""
        if not history:
            return "저장할 대화가 없습니다."
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"conversations/conversation_{timestamp}.json"
        
        os.makedirs("conversations", exist_ok=True)
        
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(history, f, ensure_ascii=False, indent=2)
            return f"대화가 {filename}에 저장되었습니다."
        except Exception as e:
            return f"저장 중 오류 발생: {e}"
    
    def create_interface(self):
        """Gradio 인터페이스 생성"""
        # 커스텀 CSS
        custom_css = """
        .gradio-container {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .chat-message {
            border-radius: 10px;
            padding: 10px;
            margin: 5px 0;
        }
        """
        
        with gr.Blocks(
            title=f"{self.name} - AI 어시스턴트", 
            theme=gr.themes.Soft(),
            css=custom_css
        ) as interface:
            
            gr.Markdown(f"""
            # 🤖 {self.name} - 나만의 AI 어시스턴트
            
            안녕하세요! 저는 **{self.name}**이에요. 무엇이든 물어보세요! 
            텍스트로 채팅하거나 음성으로 대화할 수 있어요. 🎤✨
            """)
            
            with gr.Row():
                with gr.Column(scale=3):
                    # 채팅 인터페이스
                    chatbot = gr.Chatbot(
                        label=f"{self.name}와의 대화",
                        height=500,
                        avatar_images=("👤", "🤖"),
                        bubble_full_width=False
                    )
                    
                    # 텍스트 입력
                    with gr.Row():
                        msg = gr.Textbox(
                            label="메시지 입력",
                            placeholder=f"{self.name}에게 무엇이든 물어보세요...",
                            scale=4,
                            lines=2
                        )
                        send_btn = gr.Button("전송 📤", scale=1, variant="primary")
                    
                    # 음성 입력
                    with gr.Row():
                        audio_input = gr.Audio(
                            label="🎤 음성 입력 (녹음 후 자동 처리)",
                            type="filepath",
                            scale=3
                        )
                        clear_btn = gr.Button("대화 초기화 🗑️", scale=1)
                
                with gr.Column(scale=1):
                    gr.Markdown("### ⚙️ 설정 및 정보")
                    
                    with gr.Accordion("🤖 AI 정보", open=True):
                        gr.Markdown(f"""
                        **이름**: {self.name}  
                        **타입**: 친근한 AI 어시스턴트  
                        **기능**: 
                        - 💬 자연어 대화
                        - 🎤 음성 인식  
                        - 🔊 음성 합성
                        - 🧠 컨텍스트 기억
                        """)
                    
                    with gr.Accordion("📊 상태", open=False):
                        status_text = gr.Textbox(
                            label="시스템 상태",
                            value="✅ 정상 작동 중",
                            interactive=False
                        )
                    
                    with gr.Accordion("💾 대화 관리", open=False):
                        save_btn = gr.Button("대화 저장 💾", variant="secondary")
                        save_status = gr.Textbox(
                            label="저장 상태",
                            interactive=False,
                            placeholder="저장 결과가 여기에 표시됩니다"
                        )
                    
                    with gr.Accordion("ℹ️ 사용법", open=False):
                        gr.Markdown("""
                        **텍스트 채팅**:
                        1. 아래 입력창에 메시지 입력
                        2. 전송 버튼 클릭 또는 Enter
                        
                        **음성 채팅**:
                        1. 마이크 버튼 클릭하여 녹음
                        2. 녹음 완료 후 자동 처리
                        
                        **기타**:
                        - 대화 초기화: 모든 대화 삭제
                        - 대화 저장: JSON 파일로 저장
                        """)
            
            # 이벤트 핸들러
            msg.submit(self.chat, [msg, chatbot], [chatbot, msg])
            send_btn.click(self.chat, [msg, chatbot], [chatbot, msg])
            audio_input.change(self.voice_chat, [audio_input, chatbot], [chatbot, msg])
            clear_btn.click(lambda: ([], []), None, [chatbot, msg])
            save_btn.click(self.save_conversation, chatbot, save_status)
        
        return interface

def main():
    """메인 실행 함수"""
    print("=" * 50)
    print("🚀 나만의 AI 어시스턴트 시스템 시작")
    print("=" * 50)
    
    try:
        # AI 어시스턴트 인스턴스 생성
        assistant = MyAIAssistant()
        
        # 인터페이스 생성 및 실행
        interface = assistant.create_interface()
        
        print(f"✨ {assistant.name} AI 어시스턴트가 준비되었습니다!")
        print("🌐 웹 브라우저가 자동으로 열립니다...")
        print("📝 Ollama가 실행 중이어야 AI 응답이 가능합니다.")
        print("🔗 수동 접속: http://localhost:7860")
        print("=" * 50)
        
        interface.launch(
            server_name="0.0.0.0",
            server_port=7860,
            share=False,
            inbrowser=True,
            show_error=True
        )
        
    except Exception as e:
        print(f"❌ 시스템 시작 오류: {e}")
        print("📋 requirements.txt의 모든 패키지가 설치되었는지 확인해주세요.")

if __name__ == "__main__":
    main()
