"""
Discord 봇 모듈
Z-Waif 수준의 Discord 연동 기능
"""

import discord
from discord.ext import commands
import asyncio
import json
import os
from typing import Optional, Callable

class AdvancedDiscordBot:
    def __init__(self, ai_assistant):
        self.ai = ai_assistant
        self.bot = None
        self.is_running = False
        self.token = self.load_token()
        
    def load_token(self) -> Optional[str]:
        """Discord 토큰 로드"""
        try:
            # 환경 변수에서 먼저 시도
            token = os.getenv("DISCORD_TOKEN")
            if token:
                return token
            
            # 파일에서 로드
            token_file = "config/discord_token.json"
            if os.path.exists(token_file):
                with open(token_file, 'r') as f:
                    data = json.load(f)
                    return data.get("token")
            
            return None
        except Exception as e:
            print(f"Discord 토큰 로드 실패: {e}")
            return None
    
    def setup_bot(self):
        """Discord 봇 설정"""
        if not self.token:
            print("❌ Discord 토큰이 없습니다.")
            return False
        
        # 인텐트 설정
        intents = discord.Intents.default()
        intents.message_content = True
        intents.guilds = True
        intents.guild_messages = True
        
        # 봇 생성
        self.bot = commands.Bot(
            command_prefix=os.getenv("DISCORD_PREFIX", "!"),
            intents=intents,
            description=f"{self.ai.name} AI 어시스턴트"
        )
        
        # 이벤트 핸들러 등록
        self.register_events()
        self.register_commands()
        
        return True
    
    def register_events(self):
        """이벤트 핸들러 등록"""
        @self.bot.event
        async def on_ready():
            print(f"✅ Discord 봇 로그인: {self.bot.user}")
            await self.bot.change_presence(
                activity=discord.Activity(
                    type=discord.ActivityType.listening,
                    name=f"{self.ai.user_name}의 말"
                )
            )
        
        @self.bot.event
        async def on_message(message):
            # 봇 자신의 메시지 무시
            if message.author == self.bot.user:
                return
            
            # 명령어 처리
            await self.bot.process_commands(message)
            
            # DM 또는 멘션된 메시지 처리
            if isinstance(message.channel, discord.DMChannel) or self.bot.user in message.mentions:
                await self.handle_ai_message(message)
    
    def register_commands(self):
        """명령어 등록"""
        @self.bot.command(name="chat", help="AI와 대화하기")
        async def chat_command(ctx, *, message: str):
            await self.handle_ai_message(ctx.message, message)
        
        @self.bot.command(name="regen", help="마지막 응답 재생성")
        async def regen_command(ctx):
            await self.regenerate_response(ctx)
        
        @self.bot.command(name="mode", help="AI 모드 변경")
        async def mode_command(ctx, mode: str = None):
            await self.change_mode(ctx, mode)
        
        @self.bot.command(name="status", help="AI 상태 확인")
        async def status_command(ctx):
            await self.show_status(ctx)
        
        @self.bot.command(name="memory", help="기억 검색")
        async def memory_command(ctx, *, query: str):
            await self.search_memory(ctx, query)
    
    async def handle_ai_message(self, message, content: str = None):
        """AI 메시지 처리"""
        try:
            # 타이핑 표시 시작
            async with message.channel.typing():
                # 메시지 내용 추출
                if content is None:
                    content = message.content
                    # 멘션 제거
                    content = content.replace(f'<@{self.bot.user.id}>', '').strip()
                
                # Discord 컨텍스트 추가
                discord_context = {
                    "platform": "Discord",
                    "user": str(message.author),
                    "channel": str(message.channel),
                    "guild": str(message.guild) if message.guild else "DM"
                }
                
                # AI 응답 생성
                response = self.ai.generate_response_advanced(content, discord_context)
                
                # 응답 전송 (2000자 제한 처리)
                await self.send_long_message(message.channel, response)
                
                # 대화 기록 저장
                if self.ai.modules_enabled['rag']:
                    self.ai.save_conversation_to_rag(content, response, discord_context)
        
        except Exception as e:
            await message.channel.send(f"❌ 오류가 발생했습니다: {str(e)}")
            print(f"Discord 메시지 처리 오류: {e}")
    
    async def send_long_message(self, channel, message: str):
        """긴 메시지 분할 전송"""
        if len(message) <= 2000:
            await channel.send(message)
        else:
            # 2000자씩 분할
            chunks = [message[i:i+1900] for i in range(0, len(message), 1900)]
            for i, chunk in enumerate(chunks):
                if i == 0:
                    await channel.send(chunk)
                else:
                    await channel.send(f"(계속) {chunk}")
    
    async def regenerate_response(self, ctx):
        """응답 재생성"""
        try:
            # 마지막 대화 가져오기
            if not self.ai.chat_history:
                await ctx.send("재생성할 대화가 없습니다.")
                return
            
            last_user_message = None
            for msg in reversed(self.ai.chat_history):
                if msg["role"] == "user":
                    last_user_message = msg["content"]
                    break
            
            if not last_user_message:
                await ctx.send("재생성할 사용자 메시지가 없습니다.")
                return
            
            async with ctx.typing():
                # 새 응답 생성
                response = self.ai.generate_response_advanced(last_user_message)
                await self.send_long_message(ctx.channel, f"🔄 **재생성된 응답:**\n{response}")
        
        except Exception as e:
            await ctx.send(f"❌ 재생성 중 오류: {str(e)}")
    
    async def change_mode(self, ctx, mode: str):
        """AI 모드 변경"""
        valid_modes = ["normal", "gaming", "hangout", "creative"]
        
        if not mode:
            await ctx.send(f"현재 모드: `{self.ai.current_mode}`\n사용 가능한 모드: {', '.join(valid_modes)}")
            return
        
        if mode.lower() not in valid_modes:
            await ctx.send(f"❌ 잘못된 모드입니다. 사용 가능한 모드: {', '.join(valid_modes)}")
            return
        
        self.ai.current_mode = mode.lower()
        await ctx.send(f"✅ 모드가 `{mode}`로 변경되었습니다.")
    
    async def show_status(self, ctx):
        """AI 상태 표시"""
        embed = discord.Embed(
            title=f"🤖 {self.ai.name} 상태",
            color=discord.Color.blue()
        )
        
        embed.add_field(name="모드", value=self.ai.current_mode, inline=True)
        embed.add_field(name="음성 상태", value="🔊 활성" if self.ai.modules_enabled['tts'] else "🔇 비활성", inline=True)
        embed.add_field(name="메모리", value="🧠 활성" if self.ai.modules_enabled['rag'] else "❌ 비활성", inline=True)
        
        # 활성 모듈
        active_modules = [k for k, v in self.ai.modules_enabled.items() if v]
        embed.add_field(name="활성 모듈", value=", ".join(active_modules), inline=False)
        
        # 대화 통계
        total_conversations = len(self.ai.chat_history) // 2
        embed.add_field(name="총 대화 수", value=str(total_conversations), inline=True)
        
        await ctx.send(embed=embed)
    
    async def search_memory(self, ctx, query: str):
        """메모리 검색"""
        if not self.ai.modules_enabled['rag']:
            await ctx.send("❌ 메모리 기능이 비활성화되어 있습니다.")
            return
        
        try:
            memories = self.ai.search_memories(query, limit=5)
            
            if not memories:
                await ctx.send("🔍 관련된 기억을 찾을 수 없습니다.")
                return
            
            embed = discord.Embed(
                title=f"🧠 '{query}' 관련 기억",
                color=discord.Color.green()
            )
            
            for i, memory in enumerate(memories, 1):
                embed.add_field(
                    name=f"기억 {i}",
                    value=memory[:100] + "..." if len(memory) > 100 else memory,
                    inline=False
                )
            
            await ctx.send(embed=embed)
        
        except Exception as e:
            await ctx.send(f"❌ 메모리 검색 중 오류: {str(e)}")
    
    def run_bot(self):
        """봇 실행"""
        if not self.setup_bot():
            return False
        
        try:
            # 비동기 실행
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            loop.run_until_complete(self.bot.start(self.token))
            self.is_running = True
            return True
        
        except Exception as e:
            print(f"Discord 봇 실행 오류: {e}")
            return False
    
    def stop_bot(self):
        """봇 정지"""
        if self.bot and self.is_running:
            asyncio.create_task(self.bot.close())
            self.is_running = False
