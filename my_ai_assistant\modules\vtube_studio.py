"""
VTube Studio 연동 모듈
Z-Waif 수준의 아바타 연동 기능
"""

import asyncio
import json
import os
import threading
import time
import re
from typing import Dict, List, Optional
import pyvts

class VTubeStudioIntegration:
    def __init__(self, ai_assistant):
        self.ai = ai_assistant
        self.vts = None
        self.is_connected = False
        self.is_running = False
        
        # 설정
        self.api_port = int(os.getenv("VTUBE_STUDIO_PORT", "8001"))
        self.plugin_name = "Advanced AI Assistant"
        self.developer_name = "AI Assistant Team"
        
        # 감정 및 표현 매핑
        self.emotion_mapping = self.load_emotion_mapping()
        self.expression_queue = []
        self.current_expression = "neutral"
        
        # 눈 추적 설정
        self.eye_tracking_enabled = os.getenv("ENABLE_EYE_TRACKING", "true").lower() == "true"
        self.look_target = {"x": 0, "y": 0}
        
    def load_emotion_mapping(self) -> Dict:
        """감정 매핑 로드"""
        default_mapping = {
            # 긍정적 감정
            "happy": ["smile", "joy", "laugh"],
            "excited": ["excited", "energetic", "enthusiastic"],
            "love": ["heart", "love", "affection"],
            "surprised": ["surprise", "wow", "amazed"],
            
            # 부정적 감정
            "sad": ["sad", "cry", "tears"],
            "angry": ["angry", "mad", "frustrated"],
            "confused": ["confused", "puzzled", "thinking"],
            "worried": ["worried", "anxious", "concerned"],
            
            # 중성적 감정
            "neutral": ["normal", "idle", "default"],
            "thinking": ["thinking", "pondering", "considering"],
            "sleepy": ["sleepy", "tired", "yawn"],
            "shy": ["shy", "embarrassed", "blush"]
        }
        
        # 커스텀 매핑 파일이 있으면 로드
        mapping_file = "config/emotion_mapping.json"
        if os.path.exists(mapping_file):
            try:
                with open(mapping_file, 'r', encoding='utf-8') as f:
                    custom_mapping = json.load(f)
                    default_mapping.update(custom_mapping)
            except Exception as e:
                print(f"감정 매핑 로드 오류: {e}")
        
        return default_mapping
    
    def setup_vtube_studio(self):
        """VTube Studio 연결 설정"""
        try:
            self.vts = pyvts.vts(
                plugin_info={
                    "plugin_name": self.plugin_name,
                    "developer": self.developer_name,
                    "authentication_token_path": "./vtube_token.txt",
                },
                vts_api_info={
                    "version": "1.0",
                    "name": "VTubeStudioPublicAPI",
                    "port": self.api_port
                }
            )
            
            print("  ✅ VTube Studio 설정 완료")
            return True
            
        except Exception as e:
            print(f"  ❌ VTube Studio 설정 실패: {e}")
            return False
    
    async def connect_to_vtube_studio(self):
        """VTube Studio 연결"""
        try:
            await self.vts.connect()
            await self.vts.request_authenticate_token()
            await self.vts.request_authenticate()
            
            self.is_connected = True
            print("✅ VTube Studio 연결 성공")
            
            # 초기 설정
            await self.initialize_avatar()
            
            return True
            
        except Exception as e:
            print(f"❌ VTube Studio 연결 실패: {e}")
            self.is_connected = False
            return False
    
    async def initialize_avatar(self):
        """아바타 초기 설정"""
        try:
            # 사용 가능한 핫키 목록 가져오기
            response = await self.vts.request(self.vts.vts_request.requestHotKeyList())
            self.available_hotkeys = [hk["name"] for hk in response["data"]["availableHotkeys"]]
            
            # 기본 표정으로 설정
            await self.set_expression("neutral")
            
            print(f"사용 가능한 핫키: {len(self.available_hotkeys)}개")
            
        except Exception as e:
            print(f"아바타 초기화 오류: {e}")
    
    async def set_expression(self, emotion: str, intensity: float = 1.0):
        """표정 설정"""
        if not self.is_connected:
            return
        
        try:
            # 감정에 해당하는 핫키 찾기
            hotkey_names = self.emotion_mapping.get(emotion, ["neutral"])
            
            for hotkey_name in hotkey_names:
                if hotkey_name in self.available_hotkeys:
                    # 핫키 실행
                    hotkey_request = self.vts.vts_request.requestTriggerHotKey(hotkey_name)
                    await self.vts.request(hotkey_request)
                    
                    self.current_expression = emotion
                    print(f"표정 변경: {emotion} ({hotkey_name})")
                    break
            
        except Exception as e:
            print(f"표정 설정 오류: {e}")
    
    async def set_eye_tracking(self, x: float, y: float):
        """눈 추적 설정"""
        if not self.is_connected or not self.eye_tracking_enabled:
            return
        
        try:
            # 눈 위치 설정 (VTube Studio API 사용)
            self.look_target = {"x": x, "y": y}
            
            # 실제 API 호출은 VTube Studio 버전에 따라 다를 수 있음
            # 여기서는 기본적인 구현만 제공
            
        except Exception as e:
            print(f"눈 추적 설정 오류: {e}")
    
    def analyze_emotion_from_text(self, text: str) -> str:
        """텍스트에서 감정 분석"""
        text_lower = text.lower()
        
        # 감정 키워드 매칭
        emotion_keywords = {
            "happy": ["기쁘", "행복", "좋", "웃", "ㅎㅎ", "ㅋㅋ", "하하", "호호"],
            "excited": ["신나", "흥미", "재미", "와", "우와", "대박"],
            "love": ["사랑", "좋아", "♥", "❤", "💕", "💖"],
            "surprised": ["놀라", "어?", "헉", "와!", "정말?"],
            "sad": ["슬프", "우울", "ㅠㅠ", "ㅜㅜ", "눈물", "아쉽"],
            "angry": ["화나", "짜증", "분노", "어이없", "빡"],
            "confused": ["모르", "헷갈", "이해", "뭐?", "어떻게"],
            "worried": ["걱정", "불안", "염려", "괜찮"],
            "thinking": ["생각", "음", "흠", "고민", "어떨까"],
            "shy": ["부끄", "창피", "민망", "쑥스"]
        }
        
        # 키워드 매칭으로 감정 점수 계산
        emotion_scores = {}
        for emotion, keywords in emotion_keywords.items():
            score = sum(1 for keyword in keywords if keyword in text_lower)
            if score > 0:
                emotion_scores[emotion] = score
        
        # 가장 높은 점수의 감정 반환
        if emotion_scores:
            return max(emotion_scores, key=emotion_scores.get)
        
        return "neutral"
    
    def process_ai_response(self, response_text: str):
        """AI 응답 처리하여 표정 변경"""
        if not self.is_connected:
            return
        
        # 감정 분석
        detected_emotion = self.analyze_emotion_from_text(response_text)
        
        # 표정 변경 큐에 추가
        self.expression_queue.append({
            "emotion": detected_emotion,
            "timestamp": time.time(),
            "text": response_text
        })
    
    async def expression_worker(self):
        """표정 변경 작업자"""
        while self.is_running:
            try:
                if self.expression_queue:
                    expression_data = self.expression_queue.pop(0)
                    await self.set_expression(expression_data["emotion"])
                    
                    # 표정 유지 시간
                    await asyncio.sleep(2.0)
                    
                    # 기본 표정으로 복귀
                    if not self.expression_queue:  # 큐가 비어있으면
                        await self.set_expression("neutral")
                
                await asyncio.sleep(0.1)
                
            except Exception as e:
                print(f"표정 작업자 오류: {e}")
                await asyncio.sleep(1.0)
    
    async def idle_animation_worker(self):
        """유휴 애니메이션 작업자"""
        idle_expressions = ["neutral", "thinking", "sleepy"]
        last_activity = time.time()
        
        while self.is_running:
            try:
                current_time = time.time()
                
                # 5분 이상 활동이 없으면 유휴 애니메이션
                if current_time - last_activity > 300:  # 5분
                    if not self.expression_queue:
                        idle_emotion = idle_expressions[int(current_time) % len(idle_expressions)]
                        await self.set_expression(idle_emotion)
                        await asyncio.sleep(10.0)  # 10초 유지
                        await self.set_expression("neutral")
                
                # 활동 감지
                if self.ai.is_speaking or self.expression_queue:
                    last_activity = current_time
                
                await asyncio.sleep(30.0)  # 30초마다 체크
                
            except Exception as e:
                print(f"유휴 애니메이션 오류: {e}")
                await asyncio.sleep(60.0)
    
    def start_vtube_integration(self):
        """VTube Studio 연동 시작"""
        if not self.setup_vtube_studio():
            return False
        
        self.is_running = True
        
        # 연결 스레드
        connection_thread = threading.Thread(target=self.connection_worker, daemon=True)
        connection_thread.start()
        
        return True
    
    def connection_worker(self):
        """연결 작업자 (별도 스레드)"""
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        try:
            # VTube Studio 연결
            loop.run_until_complete(self.connect_to_vtube_studio())
            
            if self.is_connected:
                # 작업자 태스크 시작
                tasks = [
                    self.expression_worker(),
                    self.idle_animation_worker()
                ]
                
                loop.run_until_complete(asyncio.gather(*tasks))
        
        except Exception as e:
            print(f"VTube Studio 연결 작업자 오류: {e}")
        
        finally:
            loop.close()
    
    def stop_vtube_integration(self):
        """VTube Studio 연동 중지"""
        self.is_running = False
        
        if self.is_connected and self.vts:
            try:
                # 연결 종료는 비동기로 처리
                asyncio.create_task(self.vts.close())
                self.is_connected = False
                print("VTube Studio 연결 종료")
            except Exception as e:
                print(f"VTube Studio 연결 종료 오류: {e}")
    
    def get_status(self) -> Dict:
        """VTube Studio 상태 반환"""
        return {
            "connected": self.is_connected,
            "running": self.is_running,
            "current_expression": self.current_expression,
            "queue_length": len(self.expression_queue),
            "available_hotkeys": len(getattr(self, 'available_hotkeys', []))
        }
