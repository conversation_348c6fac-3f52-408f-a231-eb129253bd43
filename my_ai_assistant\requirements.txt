# Z-Waif 호환 AI 어시스턴트 시스템 의존성

# === 필수 라이브러리 ===
gradio>=4.0.0
requests>=2.31.0
python-dotenv>=1.0.0
colorama>=0.4.6
pyyaml>=6.0.0
humanize>=4.0.0
emoji>=2.0.0

# === 음성 처리 ===
openai-whisper>=20231117
faster-whisper>=1.0.0
pyttsx3>=2.90
pyaudio>=0.2.11
sounddevice>=0.4.6
soundfile>=0.12.1

# === AI/ML (선택사항) ===
torch>=2.0.0
transformers>=4.30.0

# === RAG 시스템 (선택사항) ===
chromadb>=0.4.0
sentence-transformers>=2.2.0

# === 음성 활동 감지 (선택사항) ===
# silero-vad>=5.1.0

# === Discord 봇 (선택사항) ===
# discord.py>=2.3.0

# === VTube Studio (선택사항) ===
# pyvts>=0.3.3

# === 컴퓨터 비전 (선택사항) ===
# opencv-python>=4.8.0
# pillow>=10.0.0

# === 핫키 시스템 ===
keyboard>=0.13.5

# === 사운드 (선택사항) ===
# pygame>=2.5.0

# === 유틸리티 ===
numpy>=1.24.0

# === Windows 전용 (선택사항) ===
# pywin32>=306  # Windows TTS용

# === 개발 도구 (선택사항) ===
# pytest>=7.0.0
# black>=23.0.0
