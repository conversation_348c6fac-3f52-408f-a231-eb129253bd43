# 나만의 AI 어시스턴트 시스템 의존성

# 웹 인터페이스
gradio>=4.0.0
fastapi>=0.100.0
uvicorn>=0.20.0

# AI/ML 라이브러리
openai-whisper>=20231117
torch>=2.0.0
torchaudio>=2.0.0

# 음성 처리
pyttsx3>=2.90
sounddevice>=0.4.6
soundfile>=0.12.1

# 유틸리티
requests>=2.31.0
numpy>=1.24.0
python-dotenv>=1.0.0

# 선택적 의존성
# openai>=1.0.0              # OpenAI API 사용시
# discord.py>=2.3.0          # Discord 봇 기능
# opencv-python>=4.8.0       # 컴퓨터 비전
# pillow>=10.0.0             # 이미지 처리
# chromadb>=0.4.0            # 벡터 데이터베이스 (RAG)
# sentence-transformers>=2.2.0  # 임베딩
