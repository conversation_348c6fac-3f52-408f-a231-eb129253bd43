# 고성능 AI 어시스턴트 시스템 의존성 (<PERSON>-<PERSON><PERSON><PERSON> 수준)

# === 웹 인터페이스 ===
gradio>=4.0.0
fastapi>=0.100.0
uvicorn>=0.20.0

# === AI/ML 라이브러리 ===
openai-whisper>=20231117
faster-whisper>=1.0.0
torch>=2.0.0
torchaudio>=2.0.0
transformers>=4.30.0

# === 음성 처리 (고급) ===
pyttsx3>=2.90
sounddevice>=0.4.6
soundfile>=0.12.1
silero-vad>=5.1.0
pyaudio>=0.2.11

# === RAG 및 메모리 시스템 ===
chromadb>=0.4.0
sentence-transformers>=2.2.0
sqlite3  # 내장 모듈

# === Discord 봇 ===
discord.py>=2.3.0

# === VTube Studio 연동 ===
pyvts>=0.3.3

# === 컴퓨터 비전 ===
opencv-python>=4.8.0
pillow>=10.0.0

# === 핫키 및 시스템 제어 ===
keyboard>=0.13.5
mouse>=0.7.1
pyautogui>=0.9.54

# === 유틸리티 ===
requests>=2.31.0
numpy>=1.24.0
pandas>=2.0.0
python-dotenv>=1.0.0
colorama>=0.4.6
asyncio  # 내장 모듈
threading  # 내장 모듈
queue  # 내장 모듈

# === 선택적 의존성 ===
openai>=1.0.0              # OpenAI API 사용시
ollama>=0.5.0              # Ollama API 사용시

# === 개발 및 디버깅 ===
pytest>=7.0.0             # 테스트
black>=23.0.0              # 코드 포매팅
flake8>=6.0.0              # 린팅
