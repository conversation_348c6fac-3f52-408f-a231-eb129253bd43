@echo off
echo ========================================
echo   고성능 AI 어시스턴트 실행 v2.0
echo    (Z-Waif 수준의 기능과 성능)
echo ========================================
echo.

REM 현재 디렉토리 설정
set "SCRIPT_DIR=%~dp0"
cd /d "%SCRIPT_DIR%"

REM 가상환경 확인
if not exist venv (
    echo ❌ 가상환경이 없습니다!
    echo install.bat을 먼저 실행해주세요.
    pause
    exit /b 1
)

echo [1/3] 가상환경 활성화 중...
call venv\Scripts\activate

echo.
echo [2/3] Ollama 서버 상태 확인 중...
ollama list >nul 2>&1
if %errorlevel% neq 0 (
    echo ⚠️  Ollama 서버가 실행되지 않았습니다.
    echo Ollama 서버를 시작합니다...
    start /b ollama serve
    timeout /t 3 >nul
    
    REM 다시 확인
    ollama list >nul 2>&1
    if %errorlevel% neq 0 (
        echo ❌ Ollama 서버 시작 실패!
        echo 수동으로 Ollama를 설치하고 실행해주세요.
        echo 1. https://ollama.ai에서 설치
        echo 2. ollama serve 명령어로 서버 시작
        echo 3. ollama pull llama2로 모델 다운로드
        pause
        exit /b 1
    )
)

echo ✅ Ollama 서버가 실행 중입니다!

echo.
echo [3/3] AI 어시스턴트 시작 중...
echo 웹 브라우저가 자동으로 열립니다...
echo 수동 접속: http://localhost:7860
echo.
echo 종료하려면 Ctrl+C를 누르세요.
echo ========================================

python main.py

echo.
echo AI 어시스턴트가 종료되었습니다.
echo 다시 실행하려면 run.bat을 실행해주세요.
pause
