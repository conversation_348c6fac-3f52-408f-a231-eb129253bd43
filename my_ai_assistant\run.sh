#!/bin/bash

echo "========================================"
echo "      나만의 AI 어시스턴트 실행"
echo "========================================"
echo

# 현재 디렉토리로 이동
cd "$(dirname "$0")"

# 가상환경 확인
if [ ! -d "venv" ]; then
    echo "❌ 가상환경이 없습니다!"
    echo "./install.sh를 먼저 실행해주세요."
    exit 1
fi

echo "[1/3] 가상환경 활성화 중..."
source venv/bin/activate

echo
echo "[2/3] Ollama 서버 상태 확인 중..."
if ! pgrep -f "ollama serve" > /dev/null; then
    echo "⚠️  Ollama 서버가 실행되지 않았습니다."
    echo "Ollama 서버를 시작합니다..."
    
    # 백그라운드에서 Ollama 서버 시작
    nohup ollama serve > /dev/null 2>&1 &
    sleep 3
    
    # 다시 확인
    if ! pgrep -f "ollama serve" > /dev/null; then
        echo "❌ Ollama 서버 시작 실패!"
        echo "수동으로 Ollama를 설치하고 실행해주세요."
        echo "1. curl -fsSL https://ollama.ai/install.sh | sh"
        echo "2. ollama serve 명령어로 서버 시작"
        echo "3. ollama pull llama2로 모델 다운로드"
        exit 1
    fi
fi

echo "✅ Ollama 서버가 실행 중입니다!"

echo
echo "[3/3] AI 어시스턴트 시작 중..."
echo "웹 브라우저가 자동으로 열립니다..."
echo "수동 접속: http://localhost:7860"
echo
echo "종료하려면 Ctrl+C를 누르세요."
echo "========================================"

python main.py

echo
echo "AI 어시스턴트가 종료되었습니다."
echo "다시 실행하려면 ./run.sh를 실행해주세요."
