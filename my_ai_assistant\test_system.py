#!/usr/bin/env python3
"""
고성능 AI 어시스턴트 시스템 테스트
"""

import unittest
import time
import threading
from unittest.mock import Mock, patch
import sys
import os

# 메인 모듈 임포트를 위한 경로 추가
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

class TestAdvancedAIAssistant(unittest.TestCase):
    """고성능 AI 어시스턴트 테스트"""
    
    def setUp(self):
        """테스트 설정"""
        # 테스트용 환경 변수 설정
        os.environ.update({
            'AI_NAME': 'TestLuna',
            'USER_NAME': 'TestUser',
            'API_TYPE': 'ollama',
            'ENABLE_DISCORD': 'false',
            'ENABLE_VTUBE': 'false',
            'ENABLE_RAG': 'false',
            'DEBUG_MODE': 'true'
        })
    
    @patch('main.WhisperModel')
    @patch('main.pyttsx3')
    @patch('main.chromadb')
    def test_system_initialization(self, mock_chromadb, mock_tts, mock_whisper):
        """시스템 초기화 테스트"""
        from main import AdvancedAIAssistant
        
        # Mock 설정
        mock_whisper.return_value = Mock()
        mock_tts.init.return_value = Mock()
        mock_chromadb.PersistentClient.return_value = Mock()
        
        # AI 어시스턴트 생성
        assistant = AdvancedAIAssistant()
        
        # 초기화 대기
        timeout = 10
        start_time = time.time()
        while not assistant.system_ready and (time.time() - start_time) < timeout:
            time.sleep(0.1)
        
        # 검증
        self.assertTrue(assistant.system_ready, "시스템이 제한 시간 내에 초기화되지 않음")
        self.assertEqual(assistant.name, 'TestLuna')
        self.assertEqual(assistant.user_name, 'TestUser')
    
    def test_personality_loading(self):
        """성격 로딩 테스트"""
        from main import AdvancedAIAssistant
        
        with patch.multiple('main', 
                          WhisperModel=Mock(), 
                          pyttsx3=Mock(), 
                          chromadb=Mock()):
            assistant = AdvancedAIAssistant()
            personality = assistant.load_personality()
            
            self.assertIn('TestLuna', personality)
            self.assertIn('TestUser', personality)
            self.assertIsInstance(personality, str)
            self.assertGreater(len(personality), 100)
    
    @patch('requests.post')
    def test_ollama_response_generation(self, mock_post):
        """Ollama 응답 생성 테스트"""
        from main import AdvancedAIAssistant
        
        # Mock 응답 설정
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {"response": "테스트 응답입니다."}
        mock_post.return_value = mock_response
        
        with patch.multiple('main', 
                          WhisperModel=Mock(), 
                          pyttsx3=Mock(), 
                          chromadb=Mock()):
            assistant = AdvancedAIAssistant()
            
            # 응답 생성 테스트
            response = assistant.generate_ollama_response("안녕하세요", [])
            
            self.assertEqual(response, "테스트 응답입니다.")
            mock_post.assert_called_once()
    
    def test_context_building(self):
        """컨텍스트 구성 테스트"""
        from main import AdvancedAIAssistant
        
        with patch.multiple('main', 
                          WhisperModel=Mock(), 
                          pyttsx3=Mock(), 
                          chromadb=Mock()):
            assistant = AdvancedAIAssistant()
            
            # 대화 히스토리 추가
            assistant.chat_history = [
                {"role": "user", "content": "안녕하세요"},
                {"role": "assistant", "content": "안녕하세요! 반갑습니다."}
            ]
            
            # 컨텍스트 구성
            context = assistant.build_advanced_context("오늘 날씨는 어때요?")
            
            self.assertIn("TestLuna", context)
            self.assertIn("TestUser", context)
            self.assertIn("안녕하세요", context)
            self.assertIn("오늘 날씨는 어때요?", context)
    
    def test_text_cleaning_for_tts(self):
        """TTS용 텍스트 정리 테스트"""
        from main import AdvancedAIAssistant
        
        with patch.multiple('main', 
                          WhisperModel=Mock(), 
                          pyttsx3=Mock(), 
                          chromadb=Mock()):
            assistant = AdvancedAIAssistant()
            
            # 테스트 텍스트
            dirty_text = "**안녕하세요!** _반갑습니다._ `코드` [링크](url) (괄호)"
            clean_text = assistant.clean_text_for_tts(dirty_text)
            
            # 특수 문자가 제거되었는지 확인
            self.assertNotIn("**", clean_text)
            self.assertNotIn("_", clean_text)
            self.assertNotIn("`", clean_text)
            self.assertNotIn("[", clean_text)
            self.assertNotIn("(", clean_text)
            
            # 기본 텍스트는 유지되는지 확인
            self.assertIn("안녕하세요", clean_text)
            self.assertIn("반갑습니다", clean_text)
    
    def test_hotkey_configuration(self):
        """핫키 설정 테스트"""
        from main import AdvancedAIAssistant
        
        with patch.multiple('main', 
                          WhisperModel=Mock(), 
                          pyttsx3=Mock(), 
                          chromadb=Mock(),
                          keyboard=Mock()):
            assistant = AdvancedAIAssistant()
            
            # 핫키 설정 확인
            self.assertIn('chat', assistant.hotkey_config)
            self.assertIn('next', assistant.hotkey_config)
            self.assertIn('emergency_stop', assistant.hotkey_config)
    
    def test_module_status_management(self):
        """모듈 상태 관리 테스트"""
        from main import AdvancedAIAssistant
        
        with patch.multiple('main', 
                          WhisperModel=Mock(), 
                          pyttsx3=Mock(), 
                          chromadb=Mock()):
            assistant = AdvancedAIAssistant()
            
            # 기본 모듈 상태 확인
            self.assertIn('voice', assistant.modules_enabled)
            self.assertIn('tts', assistant.modules_enabled)
            self.assertIn('rag', assistant.modules_enabled)
            
            # 모듈 비활성화 테스트
            assistant.modules_enabled['discord'] = False
            self.assertFalse(assistant.modules_enabled['discord'])
    
    def test_emergency_stop(self):
        """긴급 정지 테스트"""
        from main import AdvancedAIAssistant
        
        with patch.multiple('main', 
                          WhisperModel=Mock(), 
                          pyttsx3=Mock(), 
                          chromadb=Mock()):
            assistant = AdvancedAIAssistant()
            
            # 상태 설정
            assistant.is_listening = True
            assistant.is_speaking = True
            
            # 긴급 정지 실행
            assistant.emergency_stop()
            
            # 상태 확인
            self.assertFalse(assistant.is_listening)
            self.assertFalse(assistant.is_speaking)
    
    def test_mode_switching(self):
        """모드 전환 테스트"""
        from main import AdvancedAIAssistant
        
        with patch.multiple('main', 
                          WhisperModel=Mock(), 
                          pyttsx3=Mock(), 
                          chromadb=Mock()):
            assistant = AdvancedAIAssistant()
            
            # 초기 모드 확인
            initial_mode = assistant.current_mode
            
            # 모드 전환
            assistant.switch_mode()
            
            # 모드가 변경되었는지 확인
            self.assertNotEqual(assistant.current_mode, initial_mode)
    
    def test_status_html_generation(self):
        """상태 HTML 생성 테스트"""
        from main import AdvancedAIAssistant
        
        with patch.multiple('main', 
                          WhisperModel=Mock(), 
                          pyttsx3=Mock(), 
                          chromadb=Mock()):
            assistant = AdvancedAIAssistant()
            
            # 상태 HTML 생성
            status_html = assistant.get_status_html()
            
            # HTML 내용 확인
            self.assertIn('TestLuna', status_html)
            self.assertIn('status-panel', status_html)
            self.assertIn('module-', status_html)
    
    def test_stats_html_generation(self):
        """통계 HTML 생성 테스트"""
        from main import AdvancedAIAssistant
        
        with patch.multiple('main', 
                          WhisperModel=Mock(), 
                          pyttsx3=Mock(), 
                          chromadb=Mock()):
            assistant = AdvancedAIAssistant()
            
            # 통계 HTML 생성
            stats_html = assistant.get_stats_html()
            
            # HTML 내용 확인
            self.assertIn('총 대화', stats_html)
            self.assertIn('저장된 기억', stats_html)
            self.assertIn('현재 모드', stats_html)

class TestPerformance(unittest.TestCase):
    """성능 테스트"""
    
    def test_initialization_speed(self):
        """초기화 속도 테스트"""
        start_time = time.time()
        
        with patch.multiple('main', 
                          WhisperModel=Mock(), 
                          pyttsx3=Mock(), 
                          chromadb=Mock()):
            from main import AdvancedAIAssistant
            assistant = AdvancedAIAssistant()
            
            # 초기화 완료 대기
            while not assistant.system_ready:
                time.sleep(0.01)
                if time.time() - start_time > 30:  # 30초 타임아웃
                    break
        
        initialization_time = time.time() - start_time
        
        # 30초 이내에 초기화되어야 함
        self.assertLess(initialization_time, 30, 
                       f"초기화 시간이 너무 깁니다: {initialization_time:.2f}초")
        
        print(f"✅ 초기화 시간: {initialization_time:.2f}초")

def run_tests():
    """테스트 실행"""
    print("🧪 고성능 AI 어시스턴트 시스템 테스트 시작")
    print("=" * 60)
    
    # 테스트 스위트 생성
    test_suite = unittest.TestSuite()
    
    # 테스트 추가
    test_suite.addTest(unittest.makeSuite(TestAdvancedAIAssistant))
    test_suite.addTest(unittest.makeSuite(TestPerformance))
    
    # 테스트 실행
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # 결과 출력
    print("=" * 60)
    if result.wasSuccessful():
        print("✅ 모든 테스트 통과!")
    else:
        print(f"❌ 테스트 실패: {len(result.failures)} 실패, {len(result.errors)} 오류")
    
    return result.wasSuccessful()

if __name__ == "__main__":
    success = run_tests()
    sys.exit(0 if success else 1)
