"""
Z-Waif 호환 알람 모듈
Z-Waif의 alarm.py와 동일한 구조
"""

import time
import threading
from datetime import datetime, timedelta
from typing import List, Dict, Optional, Callable

import utils.settings
import utils.voice
import utils.zw_logging

# 알람 목록
active_alarms: List[Dict] = []
alarm_thread_running = False

class Alarm:
    """알람 클래스"""
    
    def __init__(self, alarm_id: str, target_time: str, message: str = "", repeat: bool = False):
        self.alarm_id = alarm_id
        self.target_time = target_time  # "HH:MM" 형식
        self.message = message or f"알람 시간입니다! ({target_time})"
        self.repeat = repeat
        self.is_active = True
        self.created_at = datetime.now()
    
    def should_trigger(self) -> bool:
        """알람이 울려야 하는지 확인"""
        if not self.is_active:
            return False
        
        current_time = datetime.now().strftime("%H:%M")
        return current_time == self.target_time
    
    def trigger(self):
        """알람 실행"""
        print(f"⏰ Alarm triggered: {self.alarm_id} at {self.target_time}")
        
        # 음성으로 알람 메시지 출력
        utils.voice.speak_line(self.message, refuse_pause=True)
        
        # 로그 기록
        utils.zw_logging.log_system_event("Alarm", f"Triggered: {self.alarm_id}")
        
        # 반복 알람이 아니면 비활성화
        if not self.repeat:
            self.is_active = False
    
    def to_dict(self) -> Dict:
        """딕셔너리로 변환"""
        return {
            "alarm_id": self.alarm_id,
            "target_time": self.target_time,
            "message": self.message,
            "repeat": self.repeat,
            "is_active": self.is_active,
            "created_at": self.created_at.isoformat()
        }

def initialize_alarm_system():
    """알람 시스템 초기화"""
    global alarm_thread_running
    
    if not utils.settings.alarm_enabled:
        return
    
    try:
        # 기본 알람 설정
        if utils.settings.alarm_time and utils.settings.alarm_time != "09:09":
            add_alarm("default", utils.settings.alarm_time, "기본 알람입니다!", repeat=True)
        
        # 알람 스레드 시작
        if not alarm_thread_running:
            alarm_thread = threading.Thread(target=alarm_worker, daemon=True)
            alarm_thread.start()
            alarm_thread_running = True
        
        print("✅ Alarm system initialized")
        utils.zw_logging.log_system_event("Alarm", "System initialized")
        
    except Exception as e:
        print(f"❌ Alarm initialization failed: {e}")
        utils.zw_logging.log_error("Alarm", e, "initialization")

def alarm_worker():
    """알람 작업자 스레드"""
    print("⏰ Alarm worker started")
    
    while alarm_thread_running:
        try:
            current_time = datetime.now()
            
            # 활성 알람 확인
            for alarm in active_alarms[:]:  # 복사본으로 순회
                if alarm.should_trigger():
                    alarm.trigger()
                    
                    # 비활성화된 알람 제거
                    if not alarm.is_active:
                        active_alarms.remove(alarm)
            
            # 1분마다 체크
            time.sleep(60)
            
        except Exception as e:
            print(f"Alarm worker error: {e}")
            utils.zw_logging.log_error("Alarm", e, "worker")
            time.sleep(60)

def add_alarm(alarm_id: str, target_time: str, message: str = "", repeat: bool = False) -> bool:
    """
    Z-Waif의 add_alarm과 동일한 함수
    새 알람 추가
    """
    try:
        # 시간 형식 검증
        datetime.strptime(target_time, "%H:%M")
        
        # 기존 알람 ID 중복 확인
        for alarm in active_alarms:
            if alarm.alarm_id == alarm_id:
                print(f"Alarm ID already exists: {alarm_id}")
                return False
        
        # 새 알람 생성
        new_alarm = Alarm(alarm_id, target_time, message, repeat)
        active_alarms.append(new_alarm)
        
        print(f"✅ Alarm added: {alarm_id} at {target_time}")
        utils.zw_logging.log_system_event("Alarm", f"Added: {alarm_id} at {target_time}")
        
        return True
        
    except ValueError:
        print(f"Invalid time format: {target_time}")
        return False
    except Exception as e:
        print(f"Add alarm error: {e}")
        utils.zw_logging.log_error("Alarm", e, f"add_alarm({alarm_id})")
        return False

def remove_alarm(alarm_id: str) -> bool:
    """알람 제거"""
    for i, alarm in enumerate(active_alarms):
        if alarm.alarm_id == alarm_id:
            del active_alarms[i]
            print(f"🗑️ Alarm removed: {alarm_id}")
            utils.zw_logging.log_system_event("Alarm", f"Removed: {alarm_id}")
            return True
    
    print(f"Alarm not found: {alarm_id}")
    return False

def list_alarms() -> List[Dict]:
    """알람 목록 반환"""
    return [alarm.to_dict() for alarm in active_alarms]

def get_alarm(alarm_id: str) -> Optional[Dict]:
    """특정 알람 정보 반환"""
    for alarm in active_alarms:
        if alarm.alarm_id == alarm_id:
            return alarm.to_dict()
    return None

def toggle_alarm(alarm_id: str) -> bool:
    """알람 활성화/비활성화 토글"""
    for alarm in active_alarms:
        if alarm.alarm_id == alarm_id:
            alarm.is_active = not alarm.is_active
            status = "activated" if alarm.is_active else "deactivated"
            print(f"🔄 Alarm {status}: {alarm_id}")
            utils.zw_logging.log_system_event("Alarm", f"{status}: {alarm_id}")
            return True
    
    print(f"Alarm not found: {alarm_id}")
    return False

def set_default_alarm(target_time: str) -> bool:
    """기본 알람 설정"""
    # 기존 기본 알람 제거
    remove_alarm("default")
    
    # 새 기본 알람 추가
    return add_alarm("default", target_time, "기본 알람입니다!", repeat=True)

def snooze_alarm(alarm_id: str, minutes: int = 5) -> bool:
    """알람 스누즈 (지정된 분 후 다시 울림)"""
    for alarm in active_alarms:
        if alarm.alarm_id == alarm_id:
            # 현재 시간에서 지정된 분 후로 설정
            current_time = datetime.now()
            snooze_time = current_time + timedelta(minutes=minutes)
            new_time = snooze_time.strftime("%H:%M")
            
            # 임시 스누즈 알람 생성
            snooze_id = f"{alarm_id}_snooze_{int(time.time())}"
            add_alarm(snooze_id, new_time, f"스누즈 알람: {alarm.message}", repeat=False)
            
            print(f"😴 Alarm snoozed: {alarm_id} for {minutes} minutes")
            utils.zw_logging.log_system_event("Alarm", f"Snoozed: {alarm_id} for {minutes}min")
            
            return True
    
    return False

def get_next_alarm() -> Optional[Dict]:
    """다음 알람 정보 반환"""
    if not active_alarms:
        return None
    
    current_time = datetime.now()
    current_time_str = current_time.strftime("%H:%M")
    
    next_alarm = None
    min_time_diff = float('inf')
    
    for alarm in active_alarms:
        if not alarm.is_active:
            continue
        
        # 오늘 또는 내일의 알람 시간 계산
        today_alarm = datetime.strptime(f"{current_time.date()} {alarm.target_time}", "%Y-%m-%d %H:%M")
        
        if today_alarm > current_time:
            # 오늘 남은 알람
            time_diff = (today_alarm - current_time).total_seconds()
        else:
            # 내일 알람
            tomorrow_alarm = today_alarm + timedelta(days=1)
            time_diff = (tomorrow_alarm - current_time).total_seconds()
        
        if time_diff < min_time_diff:
            min_time_diff = time_diff
            next_alarm = alarm
    
    return next_alarm.to_dict() if next_alarm else None

def clear_all_alarms():
    """모든 알람 삭제"""
    global active_alarms
    
    count = len(active_alarms)
    active_alarms.clear()
    
    print(f"🗑️ All alarms cleared ({count} alarms)")
    utils.zw_logging.log_system_event("Alarm", f"All alarms cleared ({count})")

def test_alarm_system() -> bool:
    """알람 시스템 테스트"""
    try:
        # 1분 후 테스트 알람 설정
        test_time = (datetime.now() + timedelta(minutes=1)).strftime("%H:%M")
        
        success = add_alarm("test", test_time, "테스트 알람입니다!", repeat=False)
        
        if success:
            print("✅ Alarm system test successful")
            print(f"Test alarm set for {test_time}")
            return True
        else:
            print("❌ Alarm system test failed")
            return False
            
    except Exception as e:
        print(f"❌ Alarm system test failed: {e}")
        return False

# 모듈 로드 시 자동 초기화
if utils.settings.alarm_enabled:
    initialize_alarm_system()
