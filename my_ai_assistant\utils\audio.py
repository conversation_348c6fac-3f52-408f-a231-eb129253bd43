"""
Z-Waif 호환 오디오 시스템
Z-Waif의 audio.py와 동일한 구조
"""

import time
import os
import wave
import threading
from typing import Optional

import pyaudio
import sounddevice as sd
import soundfile as sf
from pydub import AudioSegment
from silero_vad import load_silero_vad, read_audio, get_speech_timestamps

import utils.hotkeys
import utils.volume_listener
import utils.transcriber_translate
import utils.settings
import utils.voice

# 오디오 설정 (Z-Waif와 동일)
CHUNK = 1024
FORMAT = pyaudio.paInt16
CHANNELS = 1
RATE = 44100

# VAD 모델
vad_model = None

# 녹음 상태
is_recording = False
audio_buffer_path = "temp_audio.wav"

def initialize_audio():
    """오디오 시스템 초기화"""
    global vad_model
    
    try:
        # VAD 모델 로드 (Z-Waif와 동일)
        if utils.settings.use_silero_vad:
            vad_model = load_silero_vad()
            print("✅ Silero VAD model loaded")
        
        # PyAudio 초기화
        audio = pyaudio.PyAudio()
        
        # 사용 가능한 오디오 장치 확인
        print("Available audio devices:")
        for i in range(audio.get_device_count()):
            info = audio.get_device_info_by_index(i)
            print(f"  {i}: {info['name']}")
        
        audio.terminate()
        print("✅ Audio system initialized")
        
    except Exception as e:
        print(f"❌ Audio initialization failed: {e}")

def record_ordus() -> str:
    """
    Z-Waif의 record_ordus 함수와 동일
    음성을 녹음하고 파일 경로를 반환
    """
    global is_recording, audio_buffer_path
    
    if is_recording:
        return audio_buffer_path
    
    is_recording = True
    
    try:
        print("🎤 Recording audio...")
        
        # PyAudio를 사용한 녹음 (Z-Waif와 동일한 방식)
        audio = pyaudio.PyAudio()
        
        stream = audio.open(
            format=FORMAT,
            channels=CHANNELS,
            rate=RATE,
            input=True,
            frames_per_buffer=CHUNK
        )
        
        frames = []
        
        # 음성 감지 기반 녹음
        silence_threshold = 500  # 조정 가능
        silence_duration = 0
        max_silence = 2.0  # 2초 침묵 후 종료
        
        start_time = time.time()
        max_duration = 10.0  # 최대 10초
        
        while True:
            data = stream.read(CHUNK)
            frames.append(data)
            
            # 볼륨 레벨 체크
            volume = max(data)
            
            if volume < silence_threshold:
                silence_duration += CHUNK / RATE
            else:
                silence_duration = 0
            
            # 종료 조건
            if silence_duration > max_silence or (time.time() - start_time) > max_duration:
                break
        
        # 녹음 종료
        stream.stop_stream()
        stream.close()
        audio.terminate()
        
        # WAV 파일로 저장
        with wave.open(audio_buffer_path, 'wb') as wf:
            wf.setnchannels(CHANNELS)
            wf.setsampwidth(audio.get_sample_size(FORMAT))
            wf.setframerate(RATE)
            wf.writeframes(b''.join(frames))
        
        print(f"✅ Audio recorded: {audio_buffer_path}")
        
    except Exception as e:
        print(f"❌ Recording failed: {e}")
    
    finally:
        is_recording = False
    
    return audio_buffer_path

def record_with_vad() -> str:
    """VAD를 사용한 고급 녹음"""
    global is_recording, audio_buffer_path
    
    if is_recording or not vad_model:
        return record_ordus()  # 기본 녹음으로 폴백
    
    is_recording = True
    
    try:
        print("🎤 Recording with VAD...")
        
        # sounddevice를 사용한 녹음
        duration = 10  # 최대 10초
        sample_rate = 16000  # VAD 모델에 최적화된 샘플레이트
        
        audio_data = sd.rec(
            int(duration * sample_rate),
            samplerate=sample_rate,
            channels=1,
            dtype='float32'
        )
        sd.wait()
        
        # WAV 파일로 저장
        sf.write(audio_buffer_path, audio_data, sample_rate)
        
        # VAD로 음성 구간 감지
        wav = read_audio(audio_buffer_path, sampling_rate=sample_rate)
        speech_timestamps = get_speech_timestamps(wav, vad_model, sampling_rate=sample_rate)
        
        if speech_timestamps:
            print(f"✅ Speech detected: {len(speech_timestamps)} segments")
        else:
            print("⚠️ No speech detected")
        
    except Exception as e:
        print(f"❌ VAD recording failed: {e}")
        return record_ordus()  # 기본 녹음으로 폴백
    
    finally:
        is_recording = False
    
    return audio_buffer_path

def cleanup_audio():
    """오디오 파일 정리"""
    try:
        if os.path.exists(audio_buffer_path):
            os.remove(audio_buffer_path)
    except Exception as e:
        print(f"Audio cleanup error: {e}")

def get_audio_devices():
    """사용 가능한 오디오 장치 목록 반환"""
    devices = []
    
    try:
        audio = pyaudio.PyAudio()
        
        for i in range(audio.get_device_count()):
            info = audio.get_device_info_by_index(i)
            devices.append({
                'index': i,
                'name': info['name'],
                'channels': info['maxInputChannels'],
                'sample_rate': info['defaultSampleRate']
            })
        
        audio.terminate()
        
    except Exception as e:
        print(f"Error getting audio devices: {e}")
    
    return devices

def test_audio():
    """오디오 시스템 테스트"""
    print("🧪 Testing audio system...")
    
    try:
        # 간단한 녹음 테스트
        test_file = record_ordus()
        
        if os.path.exists(test_file):
            file_size = os.path.getsize(test_file)
            print(f"✅ Audio test successful: {file_size} bytes")
            cleanup_audio()
            return True
        else:
            print("❌ Audio test failed: No file created")
            return False
            
    except Exception as e:
        print(f"❌ Audio test failed: {e}")
        return False
