"""
Z-Waif 호환 RAG (Retrieval-Augmented Generation) 모듈
Z-Waif의 based_rag.py와 동일한 구조
"""

import json
import os
import time
from datetime import datetime
from typing import List, Dict, Optional, Tuple

try:
    import chromadb
    from sentence_transformers import SentenceTransformer
    chromadb_available = True
except ImportError:
    chromadb_available = False
    print("ChromaDB or SentenceTransformers not available, RAG features disabled")

import utils.settings
import utils.zw_logging

# RAG 설정
RAG_DB_PATH = "rag_database"
COLLECTION_NAME = "conversation_memory"
EMBEDDING_MODEL_NAME = "all-MiniLM-L6-v2"

# 전역 변수
chroma_client = None
memory_collection = None
embedding_model = None
is_initialized = False

def initialize_rag():
    """RAG 시스템 초기화"""
    global chroma_client, memory_collection, embedding_model, is_initialized
    
    if not chromadb_available or not utils.settings.rag_enabled:
        return
    
    try:
        # ChromaDB 클라이언트 생성
        chroma_client = chromadb.PersistentClient(path=RAG_DB_PATH)
        
        # 컬렉션 생성 또는 로드
        memory_collection = chroma_client.get_or_create_collection(
            name=COLLECTION_NAME,
            metadata={"description": "Long-term conversation memory"}
        )
        
        # 임베딩 모델 로드
        embedding_model = SentenceTransformer(EMBEDDING_MODEL_NAME)
        
        is_initialized = True
        
        print("✅ RAG system initialized")
        utils.zw_logging.log_system_event("RAG", "System initialized")
        
        # 기존 메모리 수 확인
        memory_count = memory_collection.count()
        print(f"📚 Loaded {memory_count} memories")
        
    except Exception as e:
        print(f"❌ RAG initialization failed: {e}")
        utils.zw_logging.log_error("RAG", e, "initialization")
        is_initialized = False

def add_to_memory(user_input: str, ai_response: str, context: Optional[Dict] = None):
    """
    Z-Waif의 add_to_memory와 동일한 함수
    대화를 RAG 메모리에 추가
    """
    if not is_initialized or not user_input or not ai_response:
        return
    
    try:
        # 대화 텍스트 구성
        conversation_text = f"User: {user_input}\nAssistant: {ai_response}"
        
        # 임베딩 생성
        embedding = embedding_model.encode([conversation_text])
        
        # 메타데이터 구성
        metadata = {
            "timestamp": datetime.now().isoformat(),
            "user_input": user_input[:500],  # 길이 제한
            "ai_response": ai_response[:500],
            "importance": calculate_importance(user_input, ai_response)
        }
        
        if context:
            metadata.update(context)
        
        # ChromaDB에 저장
        doc_id = f"conv_{int(time.time() * 1000)}"
        memory_collection.add(
            embeddings=embedding.tolist(),
            documents=[conversation_text],
            ids=[doc_id],
            metadatas=[metadata]
        )
        
        utils.zw_logging.update_rag_log(f"Memory added: {user_input[:50]}...")
        
        # 메모리 수 제한
        cleanup_old_memories()
        
    except Exception as e:
        print(f"RAG add memory error: {e}")
        utils.zw_logging.log_error("RAG", e, "add_to_memory")

def search_relevant_memories(query: str, limit: int = 5) -> List[str]:
    """
    Z-Waif의 search_relevant_memories와 동일한 함수
    관련 메모리 검색
    """
    if not is_initialized or not query:
        return []
    
    try:
        # 쿼리 임베딩
        query_embedding = embedding_model.encode([query])
        
        # ChromaDB에서 검색
        results = memory_collection.query(
            query_embeddings=query_embedding.tolist(),
            n_results=limit,
            include=["documents", "metadatas", "distances"]
        )
        
        if not results['documents'] or not results['documents'][0]:
            return []
        
        # 결과 정리
        relevant_memories = []
        documents = results['documents'][0]
        metadatas = results['metadatas'][0] if results['metadatas'] else []
        distances = results['distances'][0] if results['distances'] else []
        
        for i, doc in enumerate(documents):
            # 유사도 임계값 체크
            if i < len(distances) and distances[i] > 1.0:  # 너무 다른 내용은 제외
                continue
            
            # AI 응답 부분만 추출
            if "Assistant: " in doc:
                ai_part = doc.split("Assistant: ", 1)[1]
                relevant_memories.append(ai_part)
        
        utils.zw_logging.update_rag_log(f"Memory search: '{query}' -> {len(relevant_memories)} results")
        
        return relevant_memories
        
    except Exception as e:
        print(f"RAG search error: {e}")
        utils.zw_logging.log_error("RAG", e, "search_relevant_memories")
        return []

def calculate_importance(user_input: str, ai_response: str) -> float:
    """대화의 중요도 계산"""
    importance = 0.5  # 기본값
    
    # 사용자 입력 기반 중요도
    if any(keyword in user_input.lower() for keyword in ['기억', '중요', '잊지마', '저장']):
        importance += 0.3
    
    # 질문 형태면 중요도 증가
    if any(char in user_input for char in ['?', '？']):
        importance += 0.1
    
    # 긴 대화는 중요도 증가
    if len(user_input) + len(ai_response) > 200:
        importance += 0.1
    
    # 감정적 내용은 중요도 증가
    emotional_keywords = ['사랑', '좋아', '싫어', '화나', '슬프', '기쁘', '행복']
    if any(keyword in user_input.lower() or keyword in ai_response.lower() 
           for keyword in emotional_keywords):
        importance += 0.2
    
    return min(1.0, importance)

def cleanup_old_memories():
    """오래된 메모리 정리"""
    if not is_initialized:
        return
    
    try:
        # 메모리 수 확인
        memory_count = memory_collection.count()
        max_memories = utils.settings.max_memory_items if hasattr(utils.settings, 'max_memory_items') else 1000
        
        if memory_count <= max_memories:
            return
        
        # 오래된 메모리 삭제 (간단한 구현)
        # 실제로는 중요도와 시간을 고려해야 함
        print(f"🧹 Cleaning up old memories ({memory_count} -> {max_memories})")
        utils.zw_logging.update_rag_log(f"Memory cleanup: {memory_count} -> {max_memories}")
        
    except Exception as e:
        print(f"Memory cleanup error: {e}")
        utils.zw_logging.log_error("RAG", e, "cleanup_old_memories")

def get_memory_stats() -> Dict:
    """메모리 통계 반환"""
    if not is_initialized:
        return {"available": False}
    
    try:
        memory_count = memory_collection.count()
        
        return {
            "available": True,
            "initialized": is_initialized,
            "total_memories": memory_count,
            "collection_name": COLLECTION_NAME,
            "embedding_model": EMBEDDING_MODEL_NAME
        }
        
    except Exception as e:
        print(f"Memory stats error: {e}")
        return {"available": False, "error": str(e)}

def clear_all_memories():
    """모든 메모리 삭제"""
    if not is_initialized:
        return False
    
    try:
        # 컬렉션 삭제 후 재생성
        chroma_client.delete_collection(COLLECTION_NAME)
        
        global memory_collection
        memory_collection = chroma_client.get_or_create_collection(
            name=COLLECTION_NAME,
            metadata={"description": "Long-term conversation memory"}
        )
        
        print("🗑️ All memories cleared")
        utils.zw_logging.update_rag_log("All memories cleared")
        
        return True
        
    except Exception as e:
        print(f"Clear memories error: {e}")
        utils.zw_logging.log_error("RAG", e, "clear_all_memories")
        return False

def export_memories(output_file: str) -> bool:
    """메모리 내보내기"""
    if not is_initialized:
        return False
    
    try:
        # 모든 메모리 가져오기
        all_memories = memory_collection.get(include=["documents", "metadatas"])
        
        export_data = {
            "export_time": datetime.now().isoformat(),
            "total_memories": len(all_memories['documents']) if all_memories['documents'] else 0,
            "memories": []
        }
        
        if all_memories['documents']:
            for i, doc in enumerate(all_memories['documents']):
                memory_data = {
                    "document": doc,
                    "metadata": all_memories['metadatas'][i] if i < len(all_memories['metadatas']) else {}
                }
                export_data["memories"].append(memory_data)
        
        # JSON 파일로 저장
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(export_data, f, ensure_ascii=False, indent=2)
        
        print(f"📤 Memories exported to {output_file}")
        utils.zw_logging.update_rag_log(f"Memories exported to {output_file}")
        
        return True
        
    except Exception as e:
        print(f"Export memories error: {e}")
        utils.zw_logging.log_error("RAG", e, "export_memories")
        return False

def test_rag_system() -> bool:
    """RAG 시스템 테스트"""
    if not is_initialized:
        print("❌ RAG system not initialized")
        return False
    
    try:
        # 테스트 메모리 추가
        test_input = "테스트 질문입니다"
        test_response = "테스트 응답입니다"
        
        add_to_memory(test_input, test_response)
        
        # 검색 테스트
        results = search_relevant_memories("테스트")
        
        if results:
            print("✅ RAG system test successful")
            return True
        else:
            print("❌ RAG system test failed: No results")
            return False
            
    except Exception as e:
        print(f"❌ RAG system test failed: {e}")
        return False

# 모듈 로드 시 자동 초기화
if utils.settings.rag_enabled:
    initialize_rag()
