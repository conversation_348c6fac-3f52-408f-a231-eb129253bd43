"""
Z-Waif 호환 Cane 라이브러리 모듈
Z-Waif의 cane_lib.py와 동일한 구조
"""

import random
import time
from typing import List, Dict, Any, Optional

def get_random_float(min_val: float = 0.0, max_val: float = 1.0) -> float:
    """랜덤 실수 생성"""
    return random.uniform(min_val, max_val)

def get_random_int(min_val: int = 0, max_val: int = 100) -> int:
    """랜덤 정수 생성"""
    return random.randint(min_val, max_val)

def get_random_choice(choices: List[Any]) -> Any:
    """리스트에서 랜덤 선택"""
    if not choices:
        return None
    return random.choice(choices)

def get_timestamp() -> str:
    """현재 타임스탬프 반환"""
    return str(int(time.time()))

def format_time(timestamp: Optional[float] = None) -> str:
    """시간 포맷팅"""
    if timestamp is None:
        timestamp = time.time()
    return time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(timestamp))

def clamp(value: float, min_val: float, max_val: float) -> float:
    """값을 범위 내로 제한"""
    return max(min_val, min(max_val, value))

def lerp(a: float, b: float, t: float) -> float:
    """선형 보간"""
    return a + (b - a) * clamp(t, 0.0, 1.0)

def normalize_text(text: str) -> str:
    """텍스트 정규화"""
    if not text:
        return ""
    
    # 기본 정리
    text = text.strip()
    
    # 연속 공백 제거
    import re
    text = re.sub(r'\s+', ' ', text)
    
    return text

def safe_get(dictionary: Dict, key: str, default: Any = None) -> Any:
    """안전한 딕셔너리 접근"""
    return dictionary.get(key, default)

def is_empty(value: Any) -> bool:
    """값이 비어있는지 확인"""
    if value is None:
        return True
    if isinstance(value, (str, list, dict)):
        return len(value) == 0
    return False

def retry_on_failure(func, max_retries: int = 3, delay: float = 1.0):
    """실패 시 재시도"""
    for attempt in range(max_retries):
        try:
            return func()
        except Exception as e:
            if attempt == max_retries - 1:
                raise e
            time.sleep(delay)
    return None
