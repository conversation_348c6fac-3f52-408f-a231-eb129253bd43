"""
Z-Waif 호환 핫키 시스템
Z-Waif의 hotkeys.py와 동일한 구조
"""

import keyboard
import time
import threading
from typing import Optional

import utils.settings
import utils.audio

# 핫키 상태
is_listening = False
hotkey_thread_running = False

def chat_input_await() -> str:
    """
    Z-Waif와 동일한 채팅 입력 대기 함수
    핫키를 기다리고 해당하는 명령을 반환
    """
    global is_listening
    
    if utils.settings.hotkeys_locked:
        time.sleep(0.1)
        return "NONE"
    
    try:
        # 스페이스바 대기 (기본 채팅 핫키)
        keyboard.wait('space')
        
        # 스페이스바가 눌렸을 때
        if not is_listening:
            return "CHAT"
        
        # 다른 핫키들 체크
        if keyboard.is_pressed('ctrl+n'):
            return "NEXT"
        elif keyboard.is_pressed('ctrl+r'):
            return "REDO"
        elif keyboard.is_pressed('ctrl+shift+s'):
            return "STOP"
        elif keyboard.is_pressed('ctrl+m'):
            return "MODE"
        else:
            return "CHAT"
            
    except Exception as e:
        print(f"Hotkey error: {e}")
        return "NONE"

def setup_hotkeys():
    """핫키 시스템 설정"""
    global hotkey_thread_running
    
    if hotkey_thread_running:
        return
    
    try:
        # 전역 핫키 설정
        keyboard.add_hotkey('space', lambda: handle_hotkey("CHAT"))
        keyboard.add_hotkey('ctrl+n', lambda: handle_hotkey("NEXT"))
        keyboard.add_hotkey('ctrl+r', lambda: handle_hotkey("REDO"))
        keyboard.add_hotkey('ctrl+shift+s', lambda: handle_hotkey("STOP"))
        keyboard.add_hotkey('ctrl+m', lambda: handle_hotkey("MODE"))
        
        hotkey_thread_running = True
        print("✅ Hotkey system initialized")
        
    except Exception as e:
        print(f"❌ Hotkey setup failed: {e}")

def handle_hotkey(command: str):
    """핫키 처리 함수"""
    if utils.settings.hotkeys_locked:
        return
    
    print(f"Hotkey pressed: {command}")
    
    # 여기서 실제 명령 처리는 main.py에서 수행
    # Z-Waif와 동일한 구조 유지

def lock_hotkeys():
    """핫키 잠금"""
    utils.settings.hotkeys_locked = True
    print("🔒 Hotkeys locked")

def unlock_hotkeys():
    """핫키 잠금 해제"""
    utils.settings.hotkeys_locked = False
    print("🔓 Hotkeys unlocked")

def cleanup_hotkeys():
    """핫키 정리"""
    global hotkey_thread_running
    
    try:
        keyboard.unhook_all()
        hotkey_thread_running = False
        print("🧹 Hotkeys cleaned up")
    except Exception as e:
        print(f"Hotkey cleanup error: {e}")

# Z-Waif 호환성을 위한 추가 함수들
def is_hotkey_pressed(key: str) -> bool:
    """특정 핫키가 눌렸는지 확인"""
    try:
        return keyboard.is_pressed(key)
    except:
        return False

def wait_for_hotkey(key: str, timeout: Optional[float] = None) -> bool:
    """특정 핫키를 기다림"""
    try:
        if timeout:
            return keyboard.wait(key, timeout=timeout)
        else:
            keyboard.wait(key)
            return True
    except:
        return False
