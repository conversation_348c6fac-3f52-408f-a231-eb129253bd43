"""
Z-Waif 호환 설정 모듈
Z-Waif의 settings.py와 동일한 구조
"""

import os
from dotenv import load_dotenv

load_dotenv()

# 캐릭터 설정
char_name = os.getenv("CHAR_NAME", "Luna")
user_name = os.getenv("YOUR_NAME", "Master")

# 핫키 설정
hotkeys_locked = False

# 음성 설정
speak_shadowchats = False
speak_only_spokento = False

# LLM 설정
max_tokens = int(os.getenv("TOKEN_LIMIT", "300"))
stream_chats = os.getenv("API_STREAM_CHATS", "ON") == "ON"
newline_cut = os.getenv("NEWLINE_CUT_BOOT", "OFF") == "ON"
asterisk_ban = False
supress_rp = os.getenv("RP_SUP_BOOT", "OFF") == "ON"
stopping_strings = ["[System", "\nUser:", "---", "<|", "###"]

# 채팅 설정
semi_auto_chat = False
hangout_mode = False
use_chatpops = os.getenv("USE_CHATPOPS", "ON") == "ON"
chatpop_phrases = []

# 자동 채팅 설정
autochat_mininum_chat_frames = int(os.getenv("AUTOCHAT_MIN_LENGTH", "149"))
use_silero_vad = os.getenv("SILERO_VAD", "ON") == "ON"

# 알람 설정
alarm_time = "09:09"
model_preset = "Default"

# 카메라 설정
cam_use_image_feed = False
cam_direct_talk = True
cam_image_preview = True
cam_use_screenshot = False

# 눈 추적 설정 ("Faces", "Random", "None")
eyes_follow = os.getenv("EYES_FOLLOW", "None")

# 태그 및 작업 설정
all_tag_list = []
cur_tags = []
all_task_char_list = []
cur_task_char = "None"

# 게임 모드 설정
is_gaming_loop = False

# 모듈 활성화 설정
minecraft_enabled = os.getenv("MODULE_MINECRAFT", "OFF") == "ON"
gaming_enabled = os.getenv("MODULE_GAMING", "OFF") == "ON"
alarm_enabled = os.getenv("MODULE_ALARM", "ON") == "ON"
vtube_enabled = os.getenv("MODULE_VTUBE", "ON") == "ON"
discord_enabled = os.getenv("MODULE_DISCORD", "OFF") == "ON"
rag_enabled = os.getenv("MODULE_RAG", "ON") == "ON"
vision_enabled = os.getenv("MODULE_VISUAL", "ON") == "ON"

# API 설정
api_type = os.getenv("API_TYPE", "OOBABOOGA")
api_type_visual = os.getenv("API_TYPE_VISUAL", "OOBABOOGA")
host_port = os.getenv("HOST_PORT", "127.0.0.1:5000")
img_port = os.getenv("IMG_PORT", "127.0.0.1:5000")

# Whisper 설정
whisper_choice = os.getenv("WHISPER_CHOICE", "faster-whisper")
whisper_model = os.getenv("WHISPER_MODEL", "base")
faster_whisper = os.getenv("FASTER_WHISPER", "ON") == "ON"
faster_whisper_cpu = os.getenv("FASTER_WHISPER_CPU_TRANSCRIPTION", "OFF") == "ON"
whisper_chunky = os.getenv("WHISPER_CHUNKY", "OFF") == "ON"
whisper_chunky_rate = int(os.getenv("WHISPER_CHUNKY_RATE", "3"))
whisper_chunks_max = int(os.getenv("WHISPER_CHUNKS_MAX", "10"))

# 시간 인코딩 설정
time_in_encoding = os.getenv("TIME_IN_ENCODING", "ON") == "ON"

# 메시지 페어 제한
message_pair_limit = int(os.getenv("MESSAGE_PAIR_LIMIT", "20"))

# VTube Studio 설정
vtube_studio_api_port = int(os.getenv("VTUBE_STUDIO_API_PORT", "8001"))

# Ollama 설정
zw_ollama_model = os.getenv("ZW_OLLAMA_MODEL", "llama2")
zw_ollama_model_visual = os.getenv("ZW_OLLAMA_MODEL_VISUAL", "llava")

# 비주얼 설정
visual_character_name = os.getenv("VISUAL_CHARACTER_NAME", "Assistant")
visual_preset_name = os.getenv("VISUAL_PRESET_NAME", "Default")
ollama_visual_encode_guidance = os.getenv("OLLAMA_VISUAL_ENCODE_GUIDANCE", "ON") == "ON"
ollama_visual_card = os.getenv("OLLAMA_VISUAL_CARD", "ON") == "ON"

# 캐릭터 카드 설정
character_card = os.getenv("CHARACTER_CARD", "CharacterCard.yaml")

def load_settings():
    """설정 로드 함수"""
    global chatpop_phrases
    
    # Chatpops 로드
    try:
        with open("Configurables/Chatpops.json", 'r', encoding='utf-8') as f:
            chatpop_phrases = json.load(f)
    except FileNotFoundError:
        chatpop_phrases = []
        print("Chatpops.json not found, using empty list")
    
    print(f"Settings loaded: {char_name} AI Assistant")
    print(f"Modules enabled: Minecraft={minecraft_enabled}, Gaming={gaming_enabled}, "
          f"Alarm={alarm_enabled}, VTube={vtube_enabled}, Discord={discord_enabled}, "
          f"RAG={rag_enabled}, Vision={vision_enabled}")

def save_settings():
    """설정 저장 함수"""
    # 필요시 설정을 파일로 저장
    pass
