"""
Z-Waif 호환 사운드보드 모듈
Z-Waif의 soundboard.py와 동일한 구조
"""

import re
import os
import threading
from typing import Optional, Dict, List

try:
    import pygame
    pygame_available = True
except ImportError:
    pygame_available = False
    print("pygame not available, soundboard features disabled")

# 사운드 파일 경로
SOUNDS_DIR = "sounds"

# 사운드 매핑 (사운드 이름 -> 파일 경로)
sound_mapping = {}

# 사운드 재생 상태
is_playing_sound = False

def initialize_soundboard():
    """사운드보드 시스템 초기화"""
    global sound_mapping
    
    if not pygame_available:
        return
    
    try:
        # pygame 초기화
        pygame.mixer.init()
        
        # 사운드 디렉토리 생성
        os.makedirs(SOUNDS_DIR, exist_ok=True)
        
        # 사운드 파일 스캔
        scan_sound_files()
        
        print(f"✅ Soundboard initialized with {len(sound_mapping)} sounds")
        
    except Exception as e:
        print(f"❌ Soundboard initialization failed: {e}")

def scan_sound_files():
    """사운드 파일 스캔 및 매핑 생성"""
    global sound_mapping
    
    if not os.path.exists(SOUNDS_DIR):
        return
    
    sound_mapping = {}
    supported_formats = ['.wav', '.mp3', '.ogg']
    
    for filename in os.listdir(SOUNDS_DIR):
        name, ext = os.path.splitext(filename)
        if ext.lower() in supported_formats:
            sound_name = name.lower()
            sound_mapping[sound_name] = os.path.join(SOUNDS_DIR, filename)
    
    print(f"Scanned {len(sound_mapping)} sound files")

def extract_soundboard(text: str) -> str:
    """
    Z-Waif의 extract_soundboard와 동일한 함수
    텍스트에서 사운드보드 명령을 추출하고 실행한 후 정리된 텍스트 반환
    """
    if not text or not pygame_available:
        return text
    
    # 사운드 패턴 찾기: [sound:name] 또는 {sound:name}
    sound_patterns = [
        r'\[sound:([^\]]+)\]',
        r'\{sound:([^\}]+)\}',
        r'<sound:([^>]+)>',
        r'\*sound:([^\*]+)\*'
    ]
    
    cleaned_text = text
    
    for pattern in sound_patterns:
        matches = re.finditer(pattern, text, re.IGNORECASE)
        
        for match in matches:
            sound_name = match.group(1).strip().lower()
            full_match = match.group(0)
            
            # 사운드 재생
            play_sound(sound_name)
            
            # 텍스트에서 사운드 명령 제거
            cleaned_text = cleaned_text.replace(full_match, '')
    
    # 연속 공백 정리
    cleaned_text = re.sub(r'\s+', ' ', cleaned_text).strip()
    
    return cleaned_text

def play_sound(sound_name: str) -> bool:
    """사운드 재생"""
    global is_playing_sound
    
    if not pygame_available or not sound_name:
        return False
    
    sound_name = sound_name.lower()
    
    if sound_name not in sound_mapping:
        print(f"Sound not found: {sound_name}")
        return False
    
    try:
        sound_path = sound_mapping[sound_name]
        
        if not os.path.exists(sound_path):
            print(f"Sound file not found: {sound_path}")
            return False
        
        # 별도 스레드에서 사운드 재생
        sound_thread = threading.Thread(
            target=_play_sound_thread,
            args=(sound_path,),
            daemon=True
        )
        sound_thread.start()
        
        return True
        
    except Exception as e:
        print(f"Sound playback error: {e}")
        return False

def _play_sound_thread(sound_path: str):
    """사운드 재생 스레드"""
    global is_playing_sound
    
    try:
        is_playing_sound = True
        
        # 사운드 로드 및 재생
        sound = pygame.mixer.Sound(sound_path)
        sound.play()
        
        # 재생 완료까지 대기
        while pygame.mixer.get_busy():
            pygame.time.wait(100)
            
    except Exception as e:
        print(f"Sound thread error: {e}")
    finally:
        is_playing_sound = False

def stop_all_sounds():
    """모든 사운드 정지"""
    if pygame_available:
        try:
            pygame.mixer.stop()
        except Exception as e:
            print(f"Stop sounds error: {e}")

def add_sound(name: str, file_path: str) -> bool:
    """새 사운드 추가"""
    global sound_mapping
    
    if not os.path.exists(file_path):
        print(f"Sound file not found: {file_path}")
        return False
    
    name = name.lower()
    sound_mapping[name] = file_path
    
    print(f"Sound added: {name} -> {file_path}")
    return True

def remove_sound(name: str) -> bool:
    """사운드 제거"""
    global sound_mapping
    
    name = name.lower()
    
    if name in sound_mapping:
        del sound_mapping[name]
        print(f"Sound removed: {name}")
        return True
    else:
        print(f"Sound not found: {name}")
        return False

def list_sounds() -> List[str]:
    """사용 가능한 사운드 목록 반환"""
    return list(sound_mapping.keys())

def get_sound_info() -> Dict[str, str]:
    """사운드 정보 반환"""
    return sound_mapping.copy()

def is_sound_playing() -> bool:
    """사운드 재생 중인지 확인"""
    return is_playing_sound

def set_volume(volume: float):
    """사운드 볼륨 설정 (0.0 - 1.0)"""
    if pygame_available:
        try:
            volume = max(0.0, min(1.0, volume))
            pygame.mixer.music.set_volume(volume)
        except Exception as e:
            print(f"Volume setting error: {e}")

def test_soundboard():
    """사운드보드 테스트"""
    print("🧪 Testing soundboard...")
    
    if not pygame_available:
        print("❌ pygame not available")
        return False
    
    # 테스트 사운드 생성 (간단한 비프음)
    try:
        import numpy as np
        
        # 440Hz 사인파 생성 (0.5초)
        sample_rate = 22050
        duration = 0.5
        frequency = 440
        
        frames = int(duration * sample_rate)
        arr = np.zeros((frames, 2))
        
        for i in range(frames):
            wave = np.sin(2 * np.pi * frequency * i / sample_rate)
            arr[i] = [wave, wave]
        
        # 16비트 정수로 변환
        arr = (arr * 32767).astype(np.int16)
        
        # pygame Sound 객체 생성
        sound = pygame.sndarray.make_sound(arr)
        sound.play()
        
        print("✅ Soundboard test successful")
        return True
        
    except Exception as e:
        print(f"❌ Soundboard test failed: {e}")
        return False

# 모듈 로드 시 자동 초기화
initialize_soundboard()
