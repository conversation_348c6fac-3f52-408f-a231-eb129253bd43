"""
Z-Waif 호환 음성 인식 모듈
Z-Waif의 transcriber_translate.py와 동일한 구조
"""

import os
import time
import threading
from typing import Optional

import whisper
from faster_whisper import WhisperModel

import utils.settings

# 환경 변수에서 설정 로드
USER_MODEL = os.environ.get("WHISPER_MODEL", "base")
WHISPER_TURBO = os.environ.get("FASTER_WHISPER", "ON")
CHUNKY_TRANSCRIPTION = os.environ.get("WHISPER_CHUNKY", "OFF")

# 모델 인스턴스
whisper_model = None
faster_whisper_model = None

# 청크 전사 관련
transcription_chunks = []
chunky_request = None

def initialize_whisper():
    """Whisper 모델 초기화"""
    global whisper_model, faster_whisper_model
    
    try:
        if WHISPER_TURBO == "ON":
            # Faster Whisper 사용
            device = "cuda" if not utils.settings.faster_whisper_cpu else "cpu"
            compute_type = "float16" if device == "cuda" else "int8"
            
            faster_whisper_model = WhisperModel(
                USER_MODEL,
                device=device,
                compute_type=compute_type
            )
            print(f"✅ Faster Whisper model loaded: {USER_MODEL} on {device}")
        else:
            # 일반 Whisper 사용
            whisper_model = whisper.load_model(USER_MODEL)
            print(f"✅ Whisper model loaded: {USER_MODEL}")
            
    except Exception as e:
        print(f"❌ Whisper initialization failed: {e}")

def transcribe_voice_to_text(voice_file: str) -> str:
    """
    Z-Waif의 transcribe_voice_to_text와 동일한 함수
    음성 파일을 텍스트로 변환
    """
    if not voice_file or not os.path.exists(voice_file):
        return ""
    
    # 청크 모드 체크
    if CHUNKY_TRANSCRIPTION == "ON":
        return chunky_get_merge()
    
    # Faster Whisper 또는 일반 Whisper 사용
    if WHISPER_TURBO == "ON":
        return faster_transcribe(voice_file)
    else:
        return classical_transcribe(voice_file)

def faster_transcribe(voice_file: str) -> str:
    """Faster Whisper를 사용한 전사"""
    global faster_whisper_model
    
    if not faster_whisper_model:
        initialize_whisper()
    
    if not faster_whisper_model:
        return "Whisper model not available"
    
    try:
        # Faster Whisper로 전사
        segments, info = faster_whisper_model.transcribe(
            voice_file,
            beam_size=5,
            language="ko",  # 한국어 우선
            temperature=0.0
        )
        
        # 세그먼트들을 텍스트로 결합
        transcript = ""
        for segment in segments:
            transcript += segment.text + " "
        
        return transcript.strip()
        
    except Exception as e:
        print(f"Faster Whisper transcription error: {e}")
        return ""

def classical_transcribe(voice_file: str) -> str:
    """일반 Whisper를 사용한 전사"""
    global whisper_model
    
    if not whisper_model:
        initialize_whisper()
    
    if not whisper_model:
        return "Whisper model not available"
    
    try:
        # 일반 Whisper로 전사
        result = whisper_model.transcribe(
            voice_file,
            language="ko",  # 한국어 우선
            temperature=0.0
        )
        
        return result["text"].strip()
        
    except Exception as e:
        print(f"Classical Whisper transcription error: {e}")
        return ""

def chunky_transcription_loop():
    """
    Z-Waif의 청크 전사 루프와 동일한 구조
    백그라운드에서 실행되는 청크 전사 처리
    """
    global transcription_chunks, chunky_request
    
    print("🔄 Chunky transcription loop started")
    
    while True:
        try:
            if chunky_request:
                # 청크 전사 처리
                chunk_result = process_chunky_request(chunky_request)
                
                if chunk_result:
                    transcription_chunks.append(chunk_result)
                    
                    # 최대 청크 수 제한
                    if len(transcription_chunks) > utils.settings.whisper_chunks_max:
                        transcription_chunks = transcription_chunks[-utils.settings.whisper_chunks_max:]
                
                chunky_request = None
            
            time.sleep(0.1)  # CPU 사용량 조절
            
        except Exception as e:
            print(f"Chunky transcription error: {e}")
            time.sleep(1.0)

def process_chunky_request(request) -> Optional[str]:
    """청크 요청 처리"""
    try:
        if not request or not os.path.exists(request):
            return None
        
        # 빠른 전사 수행
        if WHISPER_TURBO == "ON":
            return faster_transcribe(request)
        else:
            return classical_transcribe(request)
            
    except Exception as e:
        print(f"Chunky request processing error: {e}")
        return None

def chunky_get_merge() -> str:
    """
    Z-Waif의 chunky_get_merge와 동일한 함수
    청크들을 병합하여 최종 전사 결과 반환
    """
    global transcription_chunks
    
    if not transcription_chunks:
        return ""
    
    # 최근 청크들을 병합
    recent_chunks = transcription_chunks[-3:]  # 최근 3개 청크
    merged_text = " ".join(recent_chunks)
    
    # 중복 제거 및 정리
    cleaned_text = clean_transcription(merged_text)
    
    return cleaned_text

def clean_transcription(text: str) -> str:
    """전사 결과 정리"""
    if not text:
        return ""
    
    # 기본 정리
    text = text.strip()
    
    # 중복 단어 제거 (간단한 버전)
    words = text.split()
    cleaned_words = []
    
    for word in words:
        if not cleaned_words or word != cleaned_words[-1]:
            cleaned_words.append(word)
    
    return " ".join(cleaned_words)

def ordus_transcribe_voice_to_text(voice_file: str) -> str:
    """
    Z-Waif의 ordus_transcribe_voice_to_text와 동일한 함수
    Ordus 방식의 음성 전사 (실시간 처리용)
    """
    if CHUNKY_TRANSCRIPTION == "ON":
        # 청크 요청 설정
        global chunky_request
        chunky_request = voice_file
        
        # 잠시 대기 후 결과 반환
        time.sleep(0.5)
        return chunky_get_merge()
    else:
        # 일반 전사
        return transcribe_voice_to_text(voice_file)

def set_chunky_request(voice_file: str):
    """청크 요청 설정"""
    global chunky_request
    chunky_request = voice_file

def get_transcription_status() -> Dict:
    """전사 상태 반환"""
    return {
        "model": USER_MODEL,
        "faster_whisper": WHISPER_TURBO == "ON",
        "chunky_mode": CHUNKY_TRANSCRIPTION == "ON",
        "chunks_count": len(transcription_chunks),
        "model_loaded": whisper_model is not None or faster_whisper_model is not None
    }

# 모듈 로드 시 자동 초기화
initialize_whisper()
