"""
Z-Waif 호환 통합 파이프 시스템
Z-Waif의 uni_pipes.py와 동일한 구조
"""

import time
import threading
from typing import Optional, Dict, Any
from enum import Enum

import API.api_controller
import main
import utils.settings
import utils.hotkeys
import utils.hangout
import utils.zw_logging

class PipeState(Enum):
    """파이프 상태"""
    INIT = "Init"
    IDLE = "Idle"
    TTS_PROCESS = "TTS Process"
    RAG_PROCESS = "RAG Process"
    THINKING = "Thinking"
    SPEAKING = "Speaking"
    BAKED = "BAKED"

class PipeType(Enum):
    """파이프 타입"""
    TALK = "Talk"
    PICTURE = "Picture"
    DISCORD_MESSAGE = "Discord Message"
    ALARM = "Alarm"
    MEMORY = "Memory"
    GAMING = "Gaming"

class UniversalPipe:
    """
    Z-Waif의 Universal Pipe 클래스와 동일한 구조
    상태 관리 및 프로세스 제어
    """
    
    def __init__(self, pipe_id: str, pipe_type: PipeType, is_main_pipe: bool = False):
        self.pipe_id = pipe_id
        self.pipe_type = pipe_type
        self.is_main_pipe = is_main_pipe
        self.current_state = PipeState.INIT
        self.next_state = PipeState.IDLE
        self.created_time = time.time()
        self.data = {}
        
        # 스레드 관리
        self.thread = None
        self.is_running = False
        
        print(f"🔧 Pipe created: {pipe_id} ({pipe_type.value})")
    
    def start(self):
        """파이프 시작"""
        if self.is_running:
            return
        
        self.is_running = True
        self.thread = threading.Thread(target=self._run_pipe, daemon=True)
        self.thread.start()
    
    def _run_pipe(self):
        """파이프 실행 루프"""
        try:
            while self.is_running and self.current_state != PipeState.BAKED:
                self._process_current_state()
                time.sleep(0.1)  # CPU 사용량 조절
                
        except Exception as e:
            print(f"Pipe error ({self.pipe_id}): {e}")
            utils.zw_logging.update_debug_log(f"Pipe error: {e}")
        finally:
            self._cleanup()
    
    def _process_current_state(self):
        """현재 상태 처리"""
        if self.current_state == PipeState.INIT:
            self._handle_init()
        elif self.current_state == PipeState.IDLE:
            self._handle_idle()
        elif self.current_state == PipeState.TTS_PROCESS:
            self._handle_tts_process()
        elif self.current_state == PipeState.RAG_PROCESS:
            self._handle_rag_process()
        elif self.current_state == PipeState.THINKING:
            self._handle_thinking()
        elif self.current_state == PipeState.SPEAKING:
            self._handle_speaking()
        
        # 다음 상태로 전환
        if self.next_state != self.current_state:
            self.current_state = self.next_state
    
    def _handle_init(self):
        """초기화 상태 처리"""
        print(f"📋 Pipe {self.pipe_id} initializing...")
        self.next_state = PipeState.IDLE
    
    def _handle_idle(self):
        """대기 상태 처리"""
        # 파이프 타입에 따른 처리
        if self.pipe_type == PipeType.TALK:
            self.next_state = PipeState.THINKING
        elif self.pipe_type == PipeType.DISCORD_MESSAGE:
            self.next_state = PipeState.THINKING
        elif self.pipe_type == PipeType.ALARM:
            self.next_state = PipeState.THINKING
        else:
            time.sleep(0.5)
    
    def _handle_tts_process(self):
        """TTS 처리 상태"""
        print(f"🔊 Pipe {self.pipe_id} processing TTS...")
        # TTS 처리 로직
        time.sleep(1.0)  # 시뮬레이션
        self.next_state = PipeState.SPEAKING
    
    def _handle_rag_process(self):
        """RAG 처리 상태"""
        print(f"🧠 Pipe {self.pipe_id} processing RAG...")
        # RAG 처리 로직
        time.sleep(0.5)  # 시뮬레이션
        self.next_state = PipeState.THINKING
    
    def _handle_thinking(self):
        """사고 상태 (LLM 처리)"""
        print(f"🤔 Pipe {self.pipe_id} thinking...")
        
        # API 요청이 진행 중인지 확인
        if API.api_controller.is_in_api_request:
            return  # 대기
        
        # 실제 처리는 메인 함수에서 수행
        self.next_state = PipeState.TTS_PROCESS
    
    def _handle_speaking(self):
        """말하기 상태"""
        print(f"💬 Pipe {self.pipe_id} speaking...")
        
        # 음성 출력 완료 대기
        import utils.voice
        if not utils.voice.is_voice_speaking():
            self.next_state = PipeState.BAKED
    
    def _cleanup(self):
        """정리 작업"""
        print(f"🧹 Pipe {self.pipe_id} cleaned up")
        self.is_running = False
    
    def stop(self):
        """파이프 중지"""
        self.is_running = False
        self.current_state = PipeState.BAKED
    
    def get_status(self) -> Dict[str, Any]:
        """파이프 상태 반환"""
        return {
            'pipe_id': self.pipe_id,
            'pipe_type': self.pipe_type.value,
            'current_state': self.current_state.value,
            'next_state': self.next_state.value,
            'is_main_pipe': self.is_main_pipe,
            'is_running': self.is_running,
            'uptime': time.time() - self.created_time
        }

# 전역 파이프 관리
active_pipes: Dict[str, UniversalPipe] = {}
pipe_counter = 0

def start_new_pipe(desired_process: str, is_main_pipe: bool = False) -> Optional[UniversalPipe]:
    """
    Z-Waif의 start_new_pipe와 동일한 함수
    새로운 파이프 시작
    """
    global pipe_counter
    
    pipe_counter += 1
    pipe_id = f"pipe_{pipe_counter}_{desired_process}"
    
    # 파이프 타입 결정
    pipe_type = PipeType.TALK
    if "Discord" in desired_process:
        pipe_type = PipeType.DISCORD_MESSAGE
    elif "Picture" in desired_process or "Image" in desired_process:
        pipe_type = PipeType.PICTURE
    elif "Alarm" in desired_process:
        pipe_type = PipeType.ALARM
    elif "Memory" in desired_process:
        pipe_type = PipeType.MEMORY
    elif "Gaming" in desired_process:
        pipe_type = PipeType.GAMING
    
    # 파이프 생성
    pipe = UniversalPipe(pipe_id, pipe_type, is_main_pipe)
    
    # 프로세스별 처리
    if desired_process == "Main-Chat":
        threading.Thread(target=main.main_chat, daemon=True).start()
    elif desired_process == "Main-Next":
        threading.Thread(target=main.main_next, daemon=True).start()
    elif desired_process == "Main-Redo":
        threading.Thread(target=main.main_redo, daemon=True).start()
    
    # 활성 파이프에 추가
    active_pipes[pipe_id] = pipe
    pipe.start()
    
    # 오래된 파이프 정리
    cleanup_old_pipes()
    
    return pipe

def cleanup_old_pipes():
    """오래된 파이프 정리"""
    current_time = time.time()
    pipes_to_remove = []
    
    for pipe_id, pipe in active_pipes.items():
        # 5분 이상 된 파이프 또는 완료된 파이프 제거
        if (current_time - pipe.created_time > 300 or 
            pipe.current_state == PipeState.BAKED):
            pipes_to_remove.append(pipe_id)
    
    for pipe_id in pipes_to_remove:
        if pipe_id in active_pipes:
            active_pipes[pipe_id].stop()
            del active_pipes[pipe_id]

def get_active_pipes() -> Dict[str, Dict[str, Any]]:
    """활성 파이프 상태 반환"""
    return {pipe_id: pipe.get_status() for pipe_id, pipe in active_pipes.items()}

def stop_all_pipes():
    """모든 파이프 중지"""
    for pipe in active_pipes.values():
        pipe.stop()
    active_pipes.clear()
    print("🛑 All pipes stopped")

def get_main_pipe() -> Optional[UniversalPipe]:
    """메인 파이프 반환"""
    for pipe in active_pipes.values():
        if pipe.is_main_pipe and pipe.is_running:
            return pipe
    return None

def is_any_pipe_active() -> bool:
    """활성 파이프가 있는지 확인"""
    return any(pipe.is_running for pipe in active_pipes.values())

def get_pipe_by_id(pipe_id: str) -> Optional[UniversalPipe]:
    """ID로 파이프 검색"""
    return active_pipes.get(pipe_id)

def get_pipes_by_type(pipe_type: PipeType) -> List[UniversalPipe]:
    """타입별 파이프 검색"""
    return [pipe for pipe in active_pipes.values() if pipe.pipe_type == pipe_type]
