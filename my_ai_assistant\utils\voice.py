"""
Z-Waif 호환 음성 출력 모듈
Z-Waif의 voice.py와 동일한 구조
"""

import time
import os
import threading
import platform
from typing import Optional

import utils.hotkeys
import utils.voice_splitter
import utils.soundboard
import utils.settings
import API.api_controller

# 플랫폼별 TTS 엔진
if platform.system() == "Windows":
    import win32com.client
    tts_engine = None
else:
    import pyttsx3
    tts_engine = None

# 음성 상태
is_speaking = False
cut_voice = False

def initialize_voice():
    """음성 시스템 초기화"""
    global tts_engine
    
    try:
        if platform.system() == "Windows":
            # Windows SAPI 사용
            tts_engine = win32com.client.Dispatch("SAPI.SpVoice")
            
            # 음성 설정
            voices = tts_engine.GetVoices()
            for voice in voices:
                # 한국어 또는 여성 음성 우선 선택
                if any(keyword in voice.GetDescription().lower() 
                      for keyword in ['korean', 'female', 'woman', 'zira']):
                    tts_engine.Voice = voice
                    break
            
            # 속도 및 볼륨 설정
            tts_engine.Rate = 0  # 기본 속도
            tts_engine.Volume = 100  # 최대 볼륨
            
        else:
            # 다른 플랫폼에서는 pyttsx3 사용
            tts_engine = pyttsx3.init()
            
            # 음성 설정
            voices = tts_engine.getProperty('voices')
            if voices:
                for voice in voices:
                    if any(keyword in voice.name.lower() 
                          for keyword in ['korean', 'female', 'woman']):
                        tts_engine.setProperty('voice', voice.id)
                        break
            
            # 속도 및 볼륨 설정
            tts_engine.setProperty('rate', 200)
            tts_engine.setProperty('volume', 0.9)
        
        print("✅ Voice system initialized")
        
    except Exception as e:
        print(f"❌ Voice initialization failed: {e}")

def speak_line(s_message: str, refuse_pause: bool = False):
    """
    Z-Waif의 speak_line과 동일한 함수
    메시지를 음성으로 출력
    """
    global cut_voice, is_speaking
    
    if not tts_engine:
        initialize_voice()
    
    if not tts_engine:
        print("TTS engine not available")
        return
    
    cut_voice = False
    is_speaking = True
    
    try:
        # 메시지를 문장 단위로 분할 (Z-Waif와 동일)
        chunky_message = utils.voice_splitter.split_into_sentences(s_message)
        
        for chunk in chunky_message:
            if cut_voice:
                break
            
            # 사운드보드 사운드 추출 및 처리
            pure_chunk = utils.soundboard.extract_soundboard(chunk)
            
            # "말을 걸었을 때만 말하기" 설정 체크
            if (utils.settings.speak_only_spokento and 
                not API.api_controller.last_message_received_has_own_name):
                continue
            
            # 실제 음성 출력
            if pure_chunk.strip():
                speak_chunk(pure_chunk, refuse_pause)
                
                # 청크 간 간격
                if not refuse_pause and not cut_voice:
                    time.sleep(0.2)
    
    except Exception as e:
        print(f"Speech error: {e}")
    
    finally:
        is_speaking = False

def speak_chunk(chunk: str, refuse_pause: bool = False):
    """개별 청크 음성 출력"""
    global cut_voice
    
    if cut_voice or not chunk.strip():
        return
    
    try:
        if platform.system() == "Windows":
            # Windows SAPI 사용
            tts_engine.Speak(chunk)
        else:
            # pyttsx3 사용
            tts_engine.say(chunk)
            tts_engine.runAndWait()
            
    except Exception as e:
        print(f"Chunk speech error: {e}")

def stop_speaking():
    """음성 출력 중지"""
    global cut_voice, is_speaking
    
    cut_voice = True
    is_speaking = False
    
    try:
        if platform.system() == "Windows" and tts_engine:
            # Windows SAPI 중지
            tts_engine.Speak("", 2)  # SVSFPurgeBeforeSpeak flag
        elif tts_engine:
            # pyttsx3 중지
            tts_engine.stop()
            
    except Exception as e:
        print(f"Stop speaking error: {e}")

def is_voice_speaking() -> bool:
    """음성 출력 중인지 확인"""
    return is_speaking

def set_voice_rate(rate: int):
    """음성 속도 설정"""
    try:
        if platform.system() == "Windows" and tts_engine:
            # Windows SAPI: -10 ~ 10 범위
            tts_engine.Rate = max(-10, min(10, rate))
        elif tts_engine:
            # pyttsx3: 일반적으로 50-300 범위
            tts_engine.setProperty('rate', max(50, min(300, rate)))
            
    except Exception as e:
        print(f"Voice rate setting error: {e}")

def set_voice_volume(volume: float):
    """음성 볼륨 설정"""
    try:
        if platform.system() == "Windows" and tts_engine:
            # Windows SAPI: 0-100 범위
            tts_engine.Volume = max(0, min(100, int(volume * 100)))
        elif tts_engine:
            # pyttsx3: 0.0-1.0 범위
            tts_engine.setProperty('volume', max(0.0, min(1.0, volume)))
            
    except Exception as e:
        print(f"Voice volume setting error: {e}")

def get_available_voices():
    """사용 가능한 음성 목록 반환"""
    voices = []
    
    try:
        if platform.system() == "Windows" and tts_engine:
            # Windows SAPI 음성들
            sapi_voices = tts_engine.GetVoices()
            for voice in sapi_voices:
                voices.append({
                    'id': voice.Id,
                    'name': voice.GetDescription(),
                    'language': voice.GetAttribute('Language')
                })
        elif tts_engine:
            # pyttsx3 음성들
            pyttsx_voices = tts_engine.getProperty('voices')
            for voice in pyttsx_voices:
                voices.append({
                    'id': voice.id,
                    'name': voice.name,
                    'language': getattr(voice, 'languages', ['unknown'])
                })
                
    except Exception as e:
        print(f"Error getting voices: {e}")
    
    return voices

def set_voice_by_id(voice_id: str):
    """ID로 음성 설정"""
    try:
        if platform.system() == "Windows" and tts_engine:
            # Windows SAPI
            voices = tts_engine.GetVoices()
            for voice in voices:
                if voice.Id == voice_id:
                    tts_engine.Voice = voice
                    break
        elif tts_engine:
            # pyttsx3
            tts_engine.setProperty('voice', voice_id)
            
        print(f"Voice set to: {voice_id}")
        
    except Exception as e:
        print(f"Voice setting error: {e}")

def test_voice():
    """음성 시스템 테스트"""
    test_message = f"안녕하세요! 저는 {utils.settings.char_name}입니다. 음성 시스템이 정상적으로 작동하고 있습니다."
    
    print("🧪 Testing voice system...")
    speak_line(test_message, refuse_pause=True)
    
    return True

# 모듈 로드 시 자동 초기화
initialize_voice()
