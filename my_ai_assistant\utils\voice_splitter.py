"""
Z-Waif 호환 음성 분할 모듈
Z-Waif의 voice_splitter.py와 동일한 구조
"""

import re
from typing import List

def split_into_sentences(text: str) -> List[str]:
    """
    Z-Waif의 split_into_sentences와 동일한 함수
    텍스트를 문장 단위로 분할
    """
    if not text or not text.strip():
        return []
    
    # 기본 정리
    text = text.strip()
    
    # 문장 분할 패턴 (한국어 + 영어)
    sentence_endings = r'[.!?。！？]+'
    
    # 문장 분할
    sentences = re.split(sentence_endings, text)
    
    # 빈 문장 제거 및 정리
    cleaned_sentences = []
    for sentence in sentences:
        sentence = sentence.strip()
        if sentence:
            # 너무 긴 문장은 추가 분할
            if len(sentence) > 200:
                sub_sentences = split_long_sentence(sentence)
                cleaned_sentences.extend(sub_sentences)
            else:
                cleaned_sentences.append(sentence)
    
    return cleaned_sentences

def split_long_sentence(sentence: str) -> List[str]:
    """긴 문장을 더 작은 단위로 분할"""
    if len(sentence) <= 200:
        return [sentence]
    
    # 쉼표, 세미콜론 등으로 분할
    split_patterns = [',', ';', '그리고', '하지만', '그러나', '또한', '그래서']
    
    for pattern in split_patterns:
        if pattern in sentence:
            parts = sentence.split(pattern)
            if len(parts) > 1:
                result = []
                for i, part in enumerate(parts):
                    part = part.strip()
                    if part:
                        if i < len(parts) - 1:
                            part += pattern  # 분할 문자 다시 추가
                        result.append(part)
                return result
    
    # 패턴으로 분할할 수 없으면 길이로 분할
    words = sentence.split()
    chunks = []
    current_chunk = []
    current_length = 0
    
    for word in words:
        if current_length + len(word) > 150 and current_chunk:
            chunks.append(' '.join(current_chunk))
            current_chunk = [word]
            current_length = len(word)
        else:
            current_chunk.append(word)
            current_length += len(word) + 1  # +1 for space
    
    if current_chunk:
        chunks.append(' '.join(current_chunk))
    
    return chunks

def clean_for_speech(text: str) -> str:
    """
    음성 출력을 위한 텍스트 정리
    Z-Waif와 동일한 정리 로직
    """
    if not text:
        return ""
    
    # 마크다운 문법 제거
    text = re.sub(r'\*\*(.*?)\*\*', r'\1', text)  # **bold**
    text = re.sub(r'\*(.*?)\*', r'\1', text)      # *italic*
    text = re.sub(r'`(.*?)`', r'\1', text)        # `code`
    text = re.sub(r'~~(.*?)~~', r'\1', text)      # ~~strikethrough~~
    
    # 링크 제거
    text = re.sub(r'\[([^\]]+)\]\([^\)]+\)', r'\1', text)  # [text](url)
    text = re.sub(r'http[s]?://\S+', '', text)             # URLs
    
    # 특수 문자 정리
    text = re.sub(r'[#]+\s*', '', text)           # 헤더 마크
    text = re.sub(r'[>]+\s*', '', text)           # 인용문
    text = re.sub(r'[-*+]\s+', '', text)          # 리스트 마크
    
    # 이모지 및 특수 기호 처리
    text = re.sub(r':\w+:', '', text)             # :emoji:
    text = re.sub(r'[^\w\s가-힣.,!?;:()"\'-]', '', text)  # 특수문자 제거
    
    # 연속 공백 정리
    text = re.sub(r'\s+', ' ', text)
    
    return text.strip()

def add_speech_pauses(text: str) -> str:
    """음성 출력을 위한 일시정지 추가"""
    if not text:
        return ""
    
    # 문장 끝에 짧은 일시정지 추가
    text = re.sub(r'([.!?])\s*', r'\1 <break time="300ms"/> ', text)
    
    # 쉼표에 짧은 일시정지 추가
    text = re.sub(r'(,)\s*', r'\1 <break time="200ms"/> ', text)
    
    # 세미콜론에 일시정지 추가
    text = re.sub(r'(;)\s*', r'\1 <break time="400ms"/> ', text)
    
    return text

def remove_speech_pauses(text: str) -> str:
    """SSML 일시정지 태그 제거"""
    text = re.sub(r'<break[^>]*>', '', text)
    return text

def estimate_speech_duration(text: str) -> float:
    """텍스트의 예상 음성 출력 시간 계산 (초)"""
    if not text:
        return 0.0
    
    # 한국어와 영어 단어 수 계산
    korean_chars = len(re.findall(r'[가-힣]', text))
    english_words = len(re.findall(r'\b[a-zA-Z]+\b', text))
    
    # 대략적인 계산 (한국어: 3글자/초, 영어: 2단어/초)
    korean_duration = korean_chars / 3.0
    english_duration = english_words / 2.0
    
    # 문장부호로 인한 일시정지 시간 추가
    pause_count = len(re.findall(r'[.!?;,]', text))
    pause_duration = pause_count * 0.3
    
    total_duration = korean_duration + english_duration + pause_duration
    
    return max(1.0, total_duration)  # 최소 1초

def split_by_length(text: str, max_length: int = 150) -> List[str]:
    """길이 기준으로 텍스트 분할"""
    if len(text) <= max_length:
        return [text]
    
    words = text.split()
    chunks = []
    current_chunk = []
    current_length = 0
    
    for word in words:
        word_length = len(word)
        
        if current_length + word_length + 1 > max_length and current_chunk:
            chunks.append(' '.join(current_chunk))
            current_chunk = [word]
            current_length = word_length
        else:
            current_chunk.append(word)
            current_length += word_length + 1  # +1 for space
    
    if current_chunk:
        chunks.append(' '.join(current_chunk))
    
    return chunks

def is_sentence_complete(text: str) -> bool:
    """문장이 완성되었는지 확인"""
    if not text:
        return False
    
    text = text.strip()
    
    # 문장 종료 문자로 끝나는지 확인
    if text.endswith(('.', '!', '?', '。', '！', '？')):
        return True
    
    # 특정 패턴으로 끝나는 경우 (예: "입니다", "습니다" 등)
    korean_endings = ['입니다', '습니다', '였습니다', '했습니다', '됩니다', '있습니다']
    for ending in korean_endings:
        if text.endswith(ending):
            return True
    
    return False
