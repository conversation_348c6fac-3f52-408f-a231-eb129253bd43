"""
Z-Waif 호환 볼륨 리스너 모듈
Z-Waif의 volume_listener.py와 동일한 구조
"""

import time
import threading
from typing import Optional, Callable

import pyaudio
import numpy as np

# 볼륨 감지 설정
CHUNK = 1024
FORMAT = pyaudio.paInt16
CHANNELS = 1
RATE = 44100

# 볼륨 임계값
VOLUME_THRESHOLD = 500
SILENCE_DURATION = 2.0  # 2초 침묵 후 종료

# 상태 변수
is_listening = False
volume_callback: Optional[Callable] = None

def start_volume_listener(callback: Optional[Callable] = None):
    """볼륨 리스너 시작"""
    global is_listening, volume_callback
    
    if is_listening:
        return
    
    is_listening = True
    volume_callback = callback
    
    listener_thread = threading.Thread(target=_volume_listener_worker, daemon=True)
    listener_thread.start()

def _volume_listener_worker():
    """볼륨 리스너 작업자"""
    global is_listening
    
    try:
        audio = pyaudio.PyAudio()
        
        stream = audio.open(
            format=FORMAT,
            channels=CHANNELS,
            rate=RATE,
            input=True,
            frames_per_buffer=CHUNK
        )
        
        print("🎧 Volume listener started")
        
        while is_listening:
            try:
                data = stream.read(CHUNK, exception_on_overflow=False)
                volume = get_volume_level(data)
                
                if volume_callback:
                    volume_callback(volume)
                
                time.sleep(0.01)  # CPU 사용량 조절
                
            except Exception as e:
                print(f"Volume listener error: {e}")
                break
        
        stream.stop_stream()
        stream.close()
        audio.terminate()
        
    except Exception as e:
        print(f"Volume listener initialization error: {e}")
    finally:
        is_listening = False
        print("🎧 Volume listener stopped")

def stop_volume_listener():
    """볼륨 리스너 중지"""
    global is_listening
    is_listening = False

def get_volume_level(audio_data) -> float:
    """오디오 데이터에서 볼륨 레벨 계산"""
    try:
        # 바이트 데이터를 numpy 배열로 변환
        audio_array = np.frombuffer(audio_data, dtype=np.int16)
        
        # RMS (Root Mean Square) 계산
        rms = np.sqrt(np.mean(audio_array**2))
        
        return float(rms)
        
    except Exception as e:
        print(f"Volume calculation error: {e}")
        return 0.0

def is_volume_above_threshold(volume: float) -> bool:
    """볼륨이 임계값을 넘는지 확인"""
    return volume > VOLUME_THRESHOLD

def set_volume_threshold(threshold: float):
    """볼륨 임계값 설정"""
    global VOLUME_THRESHOLD
    VOLUME_THRESHOLD = threshold
    print(f"Volume threshold set to: {threshold}")

def get_current_volume() -> float:
    """현재 볼륨 레벨 반환"""
    try:
        audio = pyaudio.PyAudio()
        
        stream = audio.open(
            format=FORMAT,
            channels=CHANNELS,
            rate=RATE,
            input=True,
            frames_per_buffer=CHUNK
        )
        
        data = stream.read(CHUNK, exception_on_overflow=False)
        volume = get_volume_level(data)
        
        stream.stop_stream()
        stream.close()
        audio.terminate()
        
        return volume
        
    except Exception as e:
        print(f"Current volume check error: {e}")
        return 0.0
