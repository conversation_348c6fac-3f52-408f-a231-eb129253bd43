"""
Z-Waif 호환 VTube Studio 연동 모듈
Z-Waif의 vtube_studio.py와 동일한 구조
"""

import asyncio
import json
import os
import threading
import time
from typing import Dict, List, Optional

try:
    import pyvts
    pyvts_available = True
except ImportError:
    pyvts_available = False
    print("pyvts not available, VTube Studio features disabled")

import utils.settings
import utils.zw_logging

# VTube Studio 연결 상태
vts_client = None
is_connected = False
is_running = False

# 감정 매핑
emotion_mapping = {
    "happy": ["smile", "joy", "laugh"],
    "sad": ["sad", "cry", "tears"],
    "angry": ["angry", "mad", "frustrated"],
    "surprised": ["surprise", "wow", "amazed"],
    "neutral": ["normal", "idle", "default"],
    "thinking": ["thinking", "pondering", "hmm"],
    "excited": ["excited", "energetic", "pumped"],
    "confused": ["confused", "puzzled", "wondering"]
}

# 현재 표정
current_expression = "neutral"

def initialize_vtube_studio():
    """VTube Studio 시스템 초기화"""
    global vts_client, is_running
    
    if not pyvts_available or not utils.settings.vtube_enabled:
        return
    
    try:
        # VTS 클라이언트 생성
        vts_client = pyvts.vts(
            plugin_info={
                "plugin_name": "Z-Waif Compatible Assistant",
                "developer": "AI Assistant Team",
                "authentication_token_path": "./vtube_token.txt",
            },
            vts_api_info={
                "version": "1.0",
                "name": "VTubeStudioPublicAPI",
                "port": utils.settings.vtube_studio_api_port
            }
        )
        
        is_running = True
        
        # 연결 스레드 시작
        connection_thread = threading.Thread(target=_connection_worker, daemon=True)
        connection_thread.start()
        
        print("✅ VTube Studio system initialized")
        utils.zw_logging.log_system_event("VTubeStudio", "System initialized")
        
    except Exception as e:
        print(f"❌ VTube Studio initialization failed: {e}")
        utils.zw_logging.log_error("VTubeStudio", e, "initialization")

def _connection_worker():
    """VTube Studio 연결 작업자"""
    global is_connected
    
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    
    try:
        loop.run_until_complete(_connect_and_run())
    except Exception as e:
        print(f"VTube Studio connection error: {e}")
        utils.zw_logging.log_error("VTubeStudio", e, "connection")
    finally:
        loop.close()

async def _connect_and_run():
    """VTube Studio 연결 및 실행"""
    global is_connected, vts_client
    
    try:
        # VTube Studio에 연결
        await vts_client.connect()
        await vts_client.request_authenticate_token()
        await vts_client.request_authenticate()
        
        is_connected = True
        print("✅ Connected to VTube Studio")
        utils.zw_logging.log_system_event("VTubeStudio", "Connected successfully")
        
        # 사용 가능한 핫키 목록 가져오기
        await _load_available_hotkeys()
        
        # 연결 유지 루프
        while is_running and is_connected:
            await asyncio.sleep(1.0)
            
    except Exception as e:
        print(f"VTube Studio connection failed: {e}")
        is_connected = False
        utils.zw_logging.log_error("VTubeStudio", e, "connection failed")

async def _load_available_hotkeys():
    """사용 가능한 핫키 목록 로드"""
    try:
        response = await vts_client.request(vts_client.vts_request.requestHotKeyList())
        hotkeys = response.get("data", {}).get("availableHotkeys", [])
        
        print(f"Available VTube Studio hotkeys: {len(hotkeys)}")
        
        # 감정 매핑 업데이트
        available_hotkey_names = [hk["name"].lower() for hk in hotkeys]
        
        for emotion, expressions in emotion_mapping.items():
            # 사용 가능한 표정만 유지
            emotion_mapping[emotion] = [expr for expr in expressions if expr in available_hotkey_names]
        
    except Exception as e:
        print(f"Failed to load hotkeys: {e}")

def process_message(message: str):
    """
    Z-Waif의 process_message와 동일한 함수
    메시지를 분석하여 적절한 표정 설정
    """
    if not is_connected or not message:
        return
    
    # 감정 분석
    detected_emotion = analyze_emotion(message)
    
    if detected_emotion != current_expression:
        set_expression(detected_emotion)

def analyze_emotion(text: str) -> str:
    """텍스트에서 감정 분석"""
    if not text:
        return "neutral"
    
    text_lower = text.lower()
    
    # 감정 키워드 매칭
    emotion_keywords = {
        "happy": ["기쁘", "행복", "좋", "웃", "ㅎㅎ", "ㅋㅋ", "하하", "호호", "즐거", "신나"],
        "excited": ["신나", "흥미", "재미", "와", "우와", "대박", "최고", "멋져"],
        "sad": ["슬프", "우울", "ㅠㅠ", "ㅜㅜ", "눈물", "아쉽", "안타까", "속상"],
        "angry": ["화나", "짜증", "분노", "어이없", "빡", "열받", "못참"],
        "surprised": ["놀라", "어?", "헉", "와!", "정말?", "진짜?", "어머"],
        "confused": ["모르", "헷갈", "이해", "뭐?", "어떻게", "왜?", "음?"],
        "thinking": ["생각", "음", "흠", "고민", "어떨까", "글쎄", "아마"]
    }
    
    # 키워드 매칭으로 감정 점수 계산
    emotion_scores = {}
    for emotion, keywords in emotion_keywords.items():
        score = sum(1 for keyword in keywords if keyword in text_lower)
        if score > 0:
            emotion_scores[emotion] = score
    
    # 가장 높은 점수의 감정 반환
    if emotion_scores:
        return max(emotion_scores, key=emotion_scores.get)
    
    return "neutral"

def set_expression(emotion: str):
    """표정 설정"""
    global current_expression
    
    if not is_connected or emotion == current_expression:
        return
    
    # 비동기 표정 설정
    if is_running:
        threading.Thread(target=_set_expression_async, args=(emotion,), daemon=True).start()

def _set_expression_async(emotion: str):
    """비동기 표정 설정"""
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    
    try:
        loop.run_until_complete(_set_expression_internal(emotion))
    except Exception as e:
        print(f"Expression setting error: {e}")
    finally:
        loop.close()

async def _set_expression_internal(emotion: str):
    """내부 표정 설정 함수"""
    global current_expression
    
    try:
        # 감정에 해당하는 핫키 찾기
        expressions = emotion_mapping.get(emotion, ["neutral"])
        
        for expression in expressions:
            try:
                # 핫키 실행
                hotkey_request = vts_client.vts_request.requestTriggerHotKey(expression)
                await vts_client.request(hotkey_request)
                
                current_expression = emotion
                print(f"🎭 Expression set: {emotion} ({expression})")
                utils.zw_logging.log_system_event("VTubeStudio", f"Expression set to {emotion}")
                break
                
            except Exception as e:
                # 이 핫키가 없으면 다음 시도
                continue
        
    except Exception as e:
        print(f"Expression setting failed: {e}")

def get_connection_status() -> Dict:
    """연결 상태 반환"""
    return {
        "available": pyvts_available,
        "enabled": utils.settings.vtube_enabled,
        "connected": is_connected,
        "running": is_running,
        "current_expression": current_expression,
        "port": utils.settings.vtube_studio_api_port
    }

def disconnect():
    """VTube Studio 연결 해제"""
    global is_running, is_connected
    
    is_running = False
    is_connected = False
    
    if vts_client:
        try:
            # 연결 해제는 비동기로 처리
            threading.Thread(target=_disconnect_async, daemon=True).start()
        except Exception as e:
            print(f"Disconnect error: {e}")

def _disconnect_async():
    """비동기 연결 해제"""
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    
    try:
        if vts_client:
            loop.run_until_complete(vts_client.close())
        print("VTube Studio disconnected")
    except Exception as e:
        print(f"Disconnect error: {e}")
    finally:
        loop.close()

def test_connection() -> bool:
    """연결 테스트"""
    if not pyvts_available:
        print("❌ pyvts not available")
        return False
    
    if not is_connected:
        print("❌ Not connected to VTube Studio")
        return False
    
    # 테스트 표정 설정
    set_expression("happy")
    time.sleep(1.0)
    set_expression("neutral")
    
    print("✅ VTube Studio connection test successful")
    return True

# 모듈 로드 시 자동 초기화
if utils.settings.vtube_enabled:
    initialize_vtube_studio()
