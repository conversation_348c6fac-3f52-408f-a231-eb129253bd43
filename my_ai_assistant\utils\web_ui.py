"""Z-Waif 호환 웹 UI 모듈"""

import gradio as gr
import threading

def launch_demo():
    try:
        with gr.<PERSON><PERSON>(title="Z-Waif Compatible Assistant") as demo:
            gr.Markdown("# Z-Waif Compatible AI Assistant")
            gr.Markdown("웹 UI가 실행 중입니다.")
        
        demo.launch(server_port=7860, share=False, quiet=True)
    except Exception as e:
        print(f"Web UI error: {e}")
