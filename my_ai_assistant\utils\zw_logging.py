"""
Z-Waif 호환 로깅 시스템
Z-Waif의 zw_logging.py와 동일한 구조
"""

import os
import time
from datetime import datetime
from typing import List, Optional

# 로그 저장소 (Z-Waif와 동일)
debug_log = "General Debug log will go here!\n\nAnd here!"
rag_log = "RAG log will go here!"
kelvin_log = "Live temperature randomness will go here!"

# 로그 파일 경로
LOG_DIR = "logs"
DEBUG_LOG_FILE = os.path.join(LOG_DIR, "debug.log")
RAG_LOG_FILE = os.path.join(LOG_DIR, "rag.log")
SYSTEM_LOG_FILE = os.path.join(LOG_DIR, "system.log")

# 로그 레벨
LOG_LEVELS = {
    'DEBUG': 0,
    'INFO': 1,
    'WARNING': 2,
    'ERROR': 3,
    'CRITICAL': 4
}

current_log_level = LOG_LEVELS['INFO']

def initialize_logging():
    """로깅 시스템 초기화"""
    try:
        # 로그 디렉토리 생성
        os.makedirs(LOG_DIR, exist_ok=True)
        
        # 시스템 시작 로그
        log_system_event("System", "Logging system initialized")
        
        print("✅ Logging system initialized")
        
    except Exception as e:
        print(f"❌ Logging initialization failed: {e}")

def update_debug_log(text: str):
    """
    Z-Waif의 update_debug_log와 동일한 함수
    디버그 로그 업데이트
    """
    global debug_log
    
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    log_entry = f"[{timestamp}] {text}"
    
    debug_log += "\n\n" + log_entry
    
    # 로그 길이 제한 (메모리 관리)
    if len(debug_log) > 50000:  # 50KB 제한
        lines = debug_log.split('\n')
        debug_log = '\n'.join(lines[-500:])  # 최근 500줄만 유지
    
    # 파일에도 저장
    try:
        with open(DEBUG_LOG_FILE, 'a', encoding='utf-8') as f:
            f.write(log_entry + '\n')
    except Exception as e:
        print(f"Debug log file write error: {e}")

def update_rag_log(text: str):
    """
    Z-Waif의 update_rag_log와 동일한 함수
    RAG 로그 업데이트
    """
    global rag_log
    
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    log_entry = f"[{timestamp}] {text}"
    
    rag_log += "\n\n" + log_entry
    
    # 로그 길이 제한
    if len(rag_log) > 30000:  # 30KB 제한
        lines = rag_log.split('\n')
        rag_log = '\n'.join(lines[-300:])  # 최근 300줄만 유지
    
    # 파일에도 저장
    try:
        with open(RAG_LOG_FILE, 'a', encoding='utf-8') as f:
            f.write(log_entry + '\n')
    except Exception as e:
        print(f"RAG log file write error: {e}")

def clear_rag_log():
    """
    Z-Waif의 clear_rag_log와 동일한 함수
    RAG 로그 초기화
    """
    global rag_log
    rag_log = "RAG log cleared at " + datetime.now().strftime("%Y-%m-%d %H:%M:%S")

def update_kelvin_log(text: str):
    """
    Z-Waif의 update_kelvin_log와 동일한 함수
    Kelvin(온도) 로그 업데이트
    """
    global kelvin_log
    kelvin_log = text

def log_system_event(component: str, message: str, level: str = 'INFO'):
    """시스템 이벤트 로깅"""
    if LOG_LEVELS.get(level, 1) < current_log_level:
        return
    
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    log_entry = f"[{timestamp}] [{level}] [{component}] {message}"
    
    # 콘솔 출력 (레벨에 따라 색상 변경)
    if level == 'ERROR' or level == 'CRITICAL':
        print(f"❌ {log_entry}")
    elif level == 'WARNING':
        print(f"⚠️ {log_entry}")
    elif level == 'DEBUG':
        print(f"🐛 {log_entry}")
    else:
        print(f"ℹ️ {log_entry}")
    
    # 파일에 저장
    try:
        with open(SYSTEM_LOG_FILE, 'a', encoding='utf-8') as f:
            f.write(log_entry + '\n')
    except Exception as e:
        print(f"System log file write error: {e}")

def log_api_request(api_type: str, request_data: str, response_data: str = ""):
    """API 요청 로깅"""
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    
    log_entry = f"[{timestamp}] API Request - {api_type}\n"
    log_entry += f"Request: {request_data[:200]}...\n"  # 처음 200자만
    
    if response_data:
        log_entry += f"Response: {response_data[:200]}...\n"
    
    update_debug_log(log_entry)

def log_voice_event(event_type: str, details: str):
    """음성 관련 이벤트 로깅"""
    log_system_event("Voice", f"{event_type}: {details}")

def log_hotkey_event(hotkey: str, action: str):
    """핫키 이벤트 로깅"""
    log_system_event("Hotkey", f"{hotkey} -> {action}")

def log_error(component: str, error: Exception, context: str = ""):
    """에러 로깅"""
    error_msg = f"{str(error)}"
    if context:
        error_msg += f" (Context: {context})"
    
    log_system_event(component, error_msg, 'ERROR')

def get_debug_log() -> str:
    """디버그 로그 반환"""
    return debug_log

def get_rag_log() -> str:
    """RAG 로그 반환"""
    return rag_log

def get_kelvin_log() -> str:
    """Kelvin 로그 반환"""
    return kelvin_log

def get_recent_logs(count: int = 50) -> List[str]:
    """최근 로그 엔트리 반환"""
    try:
        with open(SYSTEM_LOG_FILE, 'r', encoding='utf-8') as f:
            lines = f.readlines()
            return lines[-count:] if lines else []
    except FileNotFoundError:
        return []
    except Exception as e:
        log_error("Logging", e, "get_recent_logs")
        return []

def search_logs(keyword: str, log_type: str = 'system') -> List[str]:
    """로그 검색"""
    results = []
    
    try:
        log_file = SYSTEM_LOG_FILE
        if log_type == 'debug':
            log_file = DEBUG_LOG_FILE
        elif log_type == 'rag':
            log_file = RAG_LOG_FILE
        
        with open(log_file, 'r', encoding='utf-8') as f:
            for line_num, line in enumerate(f, 1):
                if keyword.lower() in line.lower():
                    results.append(f"Line {line_num}: {line.strip()}")
    
    except FileNotFoundError:
        pass
    except Exception as e:
        log_error("Logging", e, f"search_logs({keyword}, {log_type})")
    
    return results

def set_log_level(level: str):
    """로그 레벨 설정"""
    global current_log_level
    
    if level.upper() in LOG_LEVELS:
        current_log_level = LOG_LEVELS[level.upper()]
        log_system_event("Logging", f"Log level set to {level.upper()}")
    else:
        log_system_event("Logging", f"Invalid log level: {level}", 'WARNING')

def cleanup_old_logs(days: int = 7):
    """오래된 로그 파일 정리"""
    try:
        current_time = time.time()
        cutoff_time = current_time - (days * 24 * 60 * 60)
        
        for filename in os.listdir(LOG_DIR):
            file_path = os.path.join(LOG_DIR, filename)
            
            if os.path.isfile(file_path):
                file_time = os.path.getmtime(file_path)
                
                if file_time < cutoff_time:
                    os.remove(file_path)
                    log_system_event("Logging", f"Removed old log file: {filename}")
    
    except Exception as e:
        log_error("Logging", e, "cleanup_old_logs")

def export_logs(output_file: str) -> bool:
    """로그 내보내기"""
    try:
        with open(output_file, 'w', encoding='utf-8') as out_f:
            # 시스템 로그
            out_f.write("=== SYSTEM LOGS ===\n")
            try:
                with open(SYSTEM_LOG_FILE, 'r', encoding='utf-8') as sys_f:
                    out_f.write(sys_f.read())
            except FileNotFoundError:
                out_f.write("No system logs found.\n")
            
            out_f.write("\n\n=== DEBUG LOGS ===\n")
            out_f.write(debug_log)
            
            out_f.write("\n\n=== RAG LOGS ===\n")
            out_f.write(rag_log)
        
        log_system_event("Logging", f"Logs exported to {output_file}")
        return True
        
    except Exception as e:
        log_error("Logging", e, f"export_logs({output_file})")
        return False

# 모듈 로드 시 자동 초기화
initialize_logging()
