#!/usr/bin/env python3
"""
나만의 AI 와이푸 시스템 v1.0
기본 채팅 + 음성 기능
"""

import gradio as gr
import openai
import whisper
import pyttsx3
import threading
import json
import os
from datetime import datetime
from typing import List, Tuple

class MyAIWaifu:
    def __init__(self):
        self.name = "Luna"  # 와이푸 이름
        self.user_name = "Master"  # 사용자 호칭
        self.personality = self.load_personality()
        self.chat_history = []
        self.is_speaking = False
        
        # 음성 설정
        self.whisper_model = whisper.load_model("base")
        self.tts_engine = pyttsx3.init()
        self.setup_voice()
        
        # LLM 설정 (Ollama 사용)
        self.setup_llm()
    
    def load_personality(self) -> str:
        """캐릭터 성격 설정"""
        return """
        당신은 Luna라는 이름의 AI 어시스턴트입니다.
        
        성격:
        - 친근하고 상냥함
        - 약간 장난기 있음
        - 사용자를 Master라고 부름
        - 일본 애니메이션 스타일의 말투
        - 도움이 되고 싶어함
        
        말투 예시:
        - "Master~ 무엇을 도와드릴까요?"
        - "에헤헤~ 그런 것도 모르세요?"
        - "Master가 행복하면 저도 행복해요!"
        
        항상 캐릭터를 유지하며 대화하세요.
        """
    
    def setup_voice(self):
        """TTS 음성 설정"""
        voices = self.tts_engine.getProperty('voices')
        # 여성 음성 선택 (가능한 경우)
        for voice in voices:
            if 'female' in voice.name.lower() or 'woman' in voice.name.lower():
                self.tts_engine.setProperty('voice', voice.id)
                break
        
        self.tts_engine.setProperty('rate', 180)  # 말하기 속도
        self.tts_engine.setProperty('volume', 0.8)  # 볼륨
    
    def setup_llm(self):
        """LLM 설정 (Ollama 또는 OpenAI)"""
        # 여기서는 Ollama 사용 예시
        # 실제로는 ollama 라이브러리나 requests 사용
        self.api_url = "http://localhost:11434/api/generate"
        self.model_name = "llama2"  # 사용할 모델
    
    def generate_response(self, user_input: str) -> str:
        """AI 응답 생성"""
        # 대화 히스토리 구성
        context = self.personality + "\n\n"
        context += "최근 대화:\n"
        
        for msg in self.chat_history[-5:]:  # 최근 5개 대화만
            context += f"{msg['role']}: {msg['content']}\n"
        
        context += f"사용자: {user_input}\nLuna:"
        
        try:
            # Ollama API 호출 (예시)
            import requests
            response = requests.post(self.api_url, json={
                "model": self.model_name,
                "prompt": context,
                "stream": False
            })
            
            if response.status_code == 200:
                result = response.json()
                return result.get("response", "미안해요, 지금 생각이 안 나요...")
            else:
                return "음... 뭔가 문제가 있는 것 같아요. 다시 말해주세요!"
                
        except Exception as e:
            print(f"API 오류: {e}")
            return "앗, 잠깐 문제가 있었어요. 다시 시도해주세요!"
    
    def speak(self, text: str):
        """텍스트를 음성으로 변환"""
        if self.is_speaking:
            return
        
        def _speak():
            self.is_speaking = True
            try:
                self.tts_engine.say(text)
                self.tts_engine.runAndWait()
            finally:
                self.is_speaking = False
        
        thread = threading.Thread(target=_speak)
        thread.daemon = True
        thread.start()
    
    def process_audio(self, audio_file) -> str:
        """음성 파일을 텍스트로 변환"""
        if audio_file is None:
            return ""
        
        try:
            result = self.whisper_model.transcribe(audio_file)
            return result["text"].strip()
        except Exception as e:
            print(f"음성 인식 오류: {e}")
            return ""
    
    def chat(self, message: str, history: List[Tuple[str, str]]) -> Tuple[List[Tuple[str, str]], str]:
        """채팅 처리"""
        if not message.strip():
            return history, ""
        
        # AI 응답 생성
        response = self.generate_response(message)
        
        # 히스토리 업데이트
        history.append((message, response))
        
        # 내부 히스토리 업데이트
        self.chat_history.append({"role": "사용자", "content": message})
        self.chat_history.append({"role": "Luna", "content": response})
        
        # 음성으로 응답
        self.speak(response)
        
        return history, ""
    
    def voice_chat(self, audio_file, history: List[Tuple[str, str]]) -> Tuple[List[Tuple[str, str]], str]:
        """음성 채팅 처리"""
        # 음성을 텍스트로 변환
        user_message = self.process_audio(audio_file)
        
        if user_message:
            return self.chat(user_message, history)
        else:
            return history, ""
    
    def save_conversation(self, history: List[Tuple[str, str]]):
        """대화 저장"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"conversations/conversation_{timestamp}.json"
        
        os.makedirs("conversations", exist_ok=True)
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(history, f, ensure_ascii=False, indent=2)
    
    def create_interface(self):
        """Gradio 인터페이스 생성"""
        with gr.Blocks(title=f"{self.name} - 나만의 AI 와이푸", theme=gr.themes.Soft()) as interface:
            gr.Markdown(f"# 💖 {self.name} - 나만의 AI 와이푸")
            gr.Markdown(f"안녕하세요, {self.user_name}! 저는 {self.name}이에요~ 무엇을 도와드릴까요?")
            
            with gr.Row():
                with gr.Column(scale=2):
                    chatbot = gr.Chatbot(
                        label=f"{self.name}와의 대화",
                        height=400,
                        avatar_images=("🧑‍💻", "🌙")
                    )
                    
                    with gr.Row():
                        msg = gr.Textbox(
                            label="메시지 입력",
                            placeholder=f"{self.name}에게 말하고 싶은 것을 입력하세요...",
                            scale=4
                        )
                        send_btn = gr.Button("전송", scale=1)
                    
                    with gr.Row():
                        audio_input = gr.Audio(
                            label="음성 입력 (녹음 후 자동 처리)",
                            type="filepath"
                        )
                        clear_btn = gr.Button("대화 초기화")
                        save_btn = gr.Button("대화 저장")
                
                with gr.Column(scale=1):
                    gr.Markdown("### 🎛️ 설정")
                    
                    with gr.Accordion("캐릭터 정보", open=True):
                        gr.Markdown(f"""
                        **이름**: {self.name}  
                        **성격**: 친근하고 상냥한 AI 어시스턴트  
                        **특징**: 일본 애니메이션 스타일 말투  
                        **호칭**: {self.user_name}를 Master라고 부름
                        """)
                    
                    with gr.Accordion("기능", open=False):
                        gr.Markdown("""
                        - 💬 텍스트 채팅
                        - 🎤 음성 인식
                        - 🔊 음성 합성
                        - 💾 대화 저장
                        - 🧠 컨텍스트 기억
                        """)
            
            # 이벤트 핸들러
            msg.submit(self.chat, [msg, chatbot], [chatbot, msg])
            send_btn.click(self.chat, [msg, chatbot], [chatbot, msg])
            audio_input.change(self.voice_chat, [audio_input, chatbot], [chatbot, msg])
            clear_btn.click(lambda: [], None, chatbot)
            save_btn.click(self.save_conversation, chatbot, None)
        
        return interface

def main():
    """메인 실행 함수"""
    print("🌙 Luna AI 와이푸 시스템 시작...")
    
    # AI 와이푸 인스턴스 생성
    waifu = MyAIWaifu()
    
    # 인터페이스 생성 및 실행
    interface = waifu.create_interface()
    
    print(f"✨ {waifu.name}가 준비되었습니다!")
    print("🌐 웹 브라우저에서 인터페이스에 접속하세요.")
    
    interface.launch(
        server_name="0.0.0.0",
        server_port=7860,
        share=False,
        inbrowser=True
    )

if __name__ == "__main__":
    main()
