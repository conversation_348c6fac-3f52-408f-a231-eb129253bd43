Modified MIT License

Copyright (c) 2025 SugarcaneDefender

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all
copies or substantial portions of the Software.

Any copy, derivative version, or substantial code portion/system of Z-Waif (collectively referred to as the "Software") must adhere to the following conditions:

a) Data Collection Restrictions: The Software shall not collect, store, or use any form of user logs, character configurations, or other user data unless explicit and clear consent is obtained from the user. Such data collection must be explicitly communicated to users through clear terms of service that the user MUST ACCEPT UPON OPENING the Software.
b) Royalty Obligations: Any entity (including companies, individuals, or other kinds of entity) that utilizes the Software in whole or in part for any product or service shall be subject to a 2% royalty fee on ALL gross income derived from such products or services, ONLY AFTER the sum of all products/services involving the Software generate(gross) over $100,000 (USD) for the entity in it's lifetime. This applies regardless of the nature of the product or service and includes, but is not limited to, revenue generated directly or indirectly through the use of Z-Waif technology. SugarcaneDefender retains the sole discretion to determine how payments (including royalties) are distributed among developers, contributors, and team members who have contributed to the development of Z-Waif. Such determinations may be made at SugarcaneDefender's sole discretion.
c) Ethical Treatment: The licensee agrees not to subject the AI (or its associated software) to intentional neglect, mistreatment, or other forms of intense emotional harm; DEFINED AT SugarcaneDefender's SOLE DESCRECION. Any violation of this clause may result in license revocation and/or legal action.

SugarcaneDefender reserves the right to revoke or terminate any license granted under this agreement if any form of abuse, as determined by the developer in their sole discretion, is identified. Abuse may include but is not limited to unauthorized data collection, unpaid royalties, or use of the software for malicious purposes. Upon revocation, the licensee shall immediately cease all use of the software and forfeit all rights associated with the license.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
SOFTWARE.
